using UnityEngine;
using System.Collections.Generic;
using System.Linq;

public class CharacterSkills
{
    readonly Dictionary<Types, SkillValues> values; // Dictionary that stores the special attack and special defense for each type

    public CharacterSkills()
    {
        values = new Dictionary<Types, SkillValues>(); // Declares the dictionary

        foreach (var type in System.Enum.GetValues(typeof(Types)))
        {
            Types enumType = (Types)type;
            string typeName = enumType.ToString();
            string acronym = typeName.Length >= 2 ? typeName[..2] : typeName;
            values.Add(enumType, new SkillValues(Random.Range(0, 16).ToString(), Random.Range(0, 16).ToString(), typeName, acronym));
        } // Fills the dictionary with random values ranging from 0 to 15
    }

    public SkillValues GetValues(Types type) => values[type]; // Returns the values of a type

    public int[] GetAllDef() // Returns an array with all the special defenses
    {
        int[] def = new int[values.Count]; // Declares the array
        for (int i = 0; i < values.Count; i++)
        {
            if (int.TryParse(values[(Types)i].spDef, out int defValue))
                def[i] = defValue;
            else
                def[i] = 0; // Default to 0 if parsing fails
        }
        return def; // Returns the array
    }

    public int[] GetAllAtk() // Returns an array with all the special attacks
    {
        int[] atk = new int[values.Count]; // Declares the array
        for (int i = 0; i < values.Count; i++)
        {
            if (int.TryParse(values[(Types)i].spAtk, out int atkValue))
                atk[i] = atkValue;
            else
                atk[i] = 0; // Default to 0 if parsing fails
        }
        return atk; // Returns the array
    }

    public void SetValues(Types type, SkillValues values) => this.values[type] = values; // Sets the values of a type

    public Types GetHighestSpDef() => values.FirstOrDefault(skill =>
    {
        if (int.TryParse(skill.Value.spDef, out int defValue))
            return defValue == Mathf.Max(GetAllDef());
        return false;
    }).Key;

    public Types GetLowestSpDef() => values.FirstOrDefault(skill =>
    {
        if (int.TryParse(skill.Value.spDef, out int defValue))
            return defValue == Mathf.Min(GetAllDef());
        return false;
    }).Key;

    public Types GetHighestSpAtk() => values.FirstOrDefault(skill =>
    {
        if (int.TryParse(skill.Value.spAtk, out int atkValue))
            return atkValue == Mathf.Max(GetAllAtk());
        return false;
    }).Key;

    public Types GetLowestSpAtk() => values.FirstOrDefault(skill =>
    {
        if (int.TryParse(skill.Value.spAtk, out int atkValue))
            return atkValue == Mathf.Min(GetAllAtk());
        return false;
    }).Key;
}

public class SkillValues
{
    public readonly string spDef, spAtk; // Special defense and special attack
    public readonly string type; // Type name (e.g., "Strength", "Magic", etc.)
    public readonly string acronym; // Type acronym (first 2 letters of type name)

    public SkillValues(string spDef, string spAtk, string type, string acronym) // Constructor of the class
    {
        this.spDef = spDef ?? "0";
        this.spAtk = spAtk ?? "0";
        this.type = type;
        this.acronym = acronym;
    }
}
