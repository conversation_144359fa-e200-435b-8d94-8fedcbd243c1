using UnityEngine;
using System.Collections.Generic;
using System.Linq;

public class CharacterSkills
{
    readonly Dictionary<Types, SkillValues> values; // Dictionary that stores the special attack and special defense for each type

    public CharacterSkills()
    {
        values = new Dictionary<Types, SkillValues>(); // Declares the dictionary

        foreach (var type in System.Enum.GetValues(typeof(Types)))
        {
            Types enumType = (Types)type;
            string typeName = enumType.ToString();
            string acronym = typeName.Length >= 2 ? typeName[..2] : typeName;
            values.Add(enumType, new SkillValues(Random.Range(0, 16), Random.Range(0, 16), typeName, acronym));
        } // Fills the dictionary with random values ranging from 0 to 15
    }

    public SkillValues GetValues(Types type) => values[type]; // Returns the values of a type

    public int[] GetAllDef() // Returns an array with all the special defenses
    {
        int[] def = new int[values.Count]; // Declares the array
        for (int i = 0; i < values.Count; i++) def[i] = values[(Types)i].spDef; // Fills the array with the special defenses
        return def; // Returns the array
    }

    public int[] GetAllAtk() // Returns an array with all the special attacks
    {
        int[] atk = new int[values.Count]; // Declares the array
        for (int i = 0; i < values.Count; i++) atk[i] = values[(Types)i].spAtk; // Fills the array with the special attacks
        return atk; // Returns the array
    }

    public void SetValues(Types type, SkillValues values) => this.values[type] = values; // Sets the values of a type

    public Types GetHighestSpDef() => values.FirstOrDefault(skill => skill.Value.spDef == Mathf.Max(GetAllDef())).Key;

    public Types GetLowestSpDef() => values.FirstOrDefault(skill => skill.Value.spDef == Mathf.Min(GetAllDef())).Key;

    public Types GetHighestSpAtk() => values.FirstOrDefault(skill => skill.Value.spAtk == Mathf.Max(GetAllAtk())).Key;

    public Types GetLowestSpAtk() => values.FirstOrDefault(skill => skill.Value.spAtk == Mathf.Min(GetAllAtk())).Key;
}

public class SkillValues
{
    public readonly int spDef, spAtk; // Special defense and special attack
    public readonly string type; // Type name (e.g., "Strength", "Magic", etc.)
    public readonly string acronym; // Type acronym (first 2 letters of type name)

    public SkillValues(int spDef, int spAtk, string type, string acronym) // Constructor of the class
    {
        this.spDef = spDef;
        this.spAtk = spAtk;
        this.type = type;
        this.acronym = acronym;
    }
}
