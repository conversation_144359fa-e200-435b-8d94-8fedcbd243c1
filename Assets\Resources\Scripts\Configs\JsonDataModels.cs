using System;
using System.Collections.Generic;
using UnityEngine;

// Wrapper classes for Lists (Unity JsonUtility can't serialize Lists directly)
[Serializable]
public class JsonCharactersList
{
    public List<JsonBattleCharacter> characters;

    public JsonCharactersList()
    {
        characters = new List<JsonBattleCharacter>();
    }

    public JsonCharactersList(List<JsonBattleCharacter> characters)
    {
        this.characters = characters ?? new List<JsonBattleCharacter>();
    }
}

[Serializable]
public class JsonSkillsList
{
    public List<JsonCharacterSkills> skills;

    public JsonSkillsList()
    {
        skills = new List<JsonCharacterSkills>();
    }

    public JsonSkillsList(List<JsonCharacterSkills> skills)
    {
        this.skills = skills ?? new List<JsonCharacterSkills>();
    }
}

[Serializable]
public class JsonStatusList
{
    public List<JsonCharacterStatus> stats;

    public JsonStatusList()
    {
        stats = new List<JsonCharacterStatus>();
    }

    public JsonStatusList(List<JsonCharacterStatus> stats)
    {
        this.stats = stats ?? new List<JsonCharacterStatus>();
    }
}

[Serializable]
public class JsonModsList
{
    public List<JsonCharacterMods> mods;

    public JsonModsList()
    {
        mods = new List<JsonCharacterMods>();
    }

    public JsonModsList(List<JsonCharacterMods> mods)
    {
        this.mods = mods ?? new List<JsonCharacterMods>();
    }
}

[Serializable]
public class JsonPartiesList
{
    public List<JsonPartyCharacters> parties;

    public JsonPartiesList()
    {
        parties = new List<JsonPartyCharacters>();
    }

    public JsonPartiesList(List<JsonPartyCharacters> parties)
    {
        this.parties = parties ?? new List<JsonPartyCharacters>();
    }
}

[Serializable]
public class JsonBuffsList
{
    public List<JsonBuffNDebuffs> buffs;

    public JsonBuffsList()
    {
        buffs = new List<JsonBuffNDebuffs>();
    }

    public JsonBuffsList(List<JsonBuffNDebuffs> buffs)
    {
        this.buffs = buffs ?? new List<JsonBuffNDebuffs>();
    }
}

/// <summary>
/// JSON-serializable data models for the save/load system
/// These classes provide a bridge between the current data structures and JSON format
/// </summary>

[Serializable]
public class JsonSkillValues
{
    public int spDef;
    public int spAtk;

    public JsonSkillValues() { }

    public JsonSkillValues(SkillValues skillValues)
    {
        spDef = skillValues.spDef;
        spAtk = skillValues.spAtk;
    }

    public SkillValues ToSkillValues()
    {
        return new SkillValues(spDef, spAtk);
    }
}

[Serializable]
public class JsonCharacterSkills
{
    public JsonSkillValues[] skillsByType; // Array indexed by Types enum values

    public JsonCharacterSkills() 
    {
        skillsByType = new JsonSkillValues[Enum.GetValues(typeof(Types)).Length];
    }

    public JsonCharacterSkills(CharacterSkills skills)
    {
        skillsByType = new JsonSkillValues[Enum.GetValues(typeof(Types)).Length];

        for (int i = 0; i < skillsByType.Length; i++)
        {
            var skillValues = skills.GetValues((Types)i);
            skillsByType[i] = new JsonSkillValues(skillValues);
        }
    }

    public CharacterSkills ToCharacterSkills()
    {
        CharacterSkills skills = new CharacterSkills();
        for (int i = 0; i < skillsByType.Length && i < Enum.GetValues(typeof(Types)).Length; i++)
        {
            if (skillsByType[i] != null)
            {
                skills.SetValues((Types)i, skillsByType[i].ToSkillValues());
            }
        }
        return skills;
    }
}

[Serializable]
public class JsonCharacterStatus
{
    public List<int> apMin;
    public List<int> hp;
    public List<int> atk;
    public List<int> def;
    public List<int> atkLim;
    public List<int> bl;

    public JsonCharacterStatus() { }

    public JsonCharacterStatus(CharacterStatus status)
    {
        apMin = new List<int>(status.GetApMin());
        hp = new List<int>(status.GetHp());
        atk = new List<int>(status.GetAtk());
        def = new List<int>(status.GetDef());
        atkLim = new List<int>(status.GetAtkLim());
        bl = new List<int>(status.GetBl());
    }

    public CharacterStatus ToCharacterStatus()
    {
        CharacterStatus status = new CharacterStatus();
        if (apMin != null) status.UpdateApMin(apMin);
        if (hp != null) status.UpdateHp(hp);
        if (atk != null) status.UpdateAtk(atk);
        if (def != null) status.UpdateDef(def);
        if (atkLim != null) status.UpdateAtkLim(atkLim);
        status.UpdateBl(); // This regenerates bl based on apMin count
        return status;
    }
}

[Serializable]
public class JsonCharacterMods
{
    public int knowledge;
    public int luck;
    public int speed;
    public int precision;
    public int evasion;
    public int criticalChance;

    public JsonCharacterMods() { }

    public JsonCharacterMods(CharacterMods mods)
    {
        knowledge = mods.GetKnowledge();
        luck = mods.GetLuck();
        speed = mods.GetSpeed();
        precision = mods.GetPrecision();
        evasion = mods.GetEvasion();
        criticalChance = mods.GetCriticalChance();
    }

    public CharacterMods ToCharacterMods()
    {
        CharacterMods mods = new CharacterMods();
        mods.SetKnowledge(knowledge);
        mods.SetLuck(luck);
        mods.SetSpeed(speed);
        mods.SetPrecision(precision);
        mods.SetEvasion(evasion);
        mods.SetCriticalChance(criticalChance);
        return mods;
    }
}

[Serializable]
public class JsonBattleCharacter
{
    public string id;
    public string name;
    public int level;
    public bool isEnemy;
    public JsonCharacterSkills skills;
    public JsonCharacterStatus stats;
    public JsonCharacterMods mods;

    public JsonBattleCharacter() { }

    public JsonBattleCharacter(BattleCharacter character)
    {
        if (character == null)
        {
            Debug.LogError("[JsonBattleCharacter] ❌ Cannot create JsonBattleCharacter from null character");
            return;
        }

        id = character.id;
        name = character.name;
        level = character.level;
        isEnemy = character.isEnemy;

        try
        {
            skills = new JsonCharacterSkills(character.skills);
        }
        catch (Exception ex)
        {
            Debug.LogError($"[JsonBattleCharacter] ❌ Failed to convert skills for character {character.id}: {ex.Message}");
            skills = new JsonCharacterSkills();
        }

        try
        {
            stats = new JsonCharacterStatus(character.stats);
        }
        catch (Exception ex)
        {
            Debug.LogError($"[JsonBattleCharacter] ❌ Failed to convert stats for character {character.id}: {ex.Message}");
            stats = new JsonCharacterStatus();
        }

        try
        {
            mods = new JsonCharacterMods(character.mods);
        }
        catch (Exception ex)
        {
            Debug.LogError($"[JsonBattleCharacter] ❌ Failed to convert mods for character {character.id}: {ex.Message}");
            mods = new JsonCharacterMods();
        }
    }

    public BattleCharacter ToBattleCharacter()
    {
        BattleCharacter character = new BattleCharacter(id);
        character.name = name;
        character.level = level;
        character.isEnemy = isEnemy;
        if (skills != null) character.skills = skills.ToCharacterSkills();
        if (stats != null) character.stats = stats.ToCharacterStatus();
        if (mods != null) character.mods = mods.ToCharacterMods();
        return character;
    }
}

[Serializable]
public class JsonBuffNDebuffs
{
    public string name;
    public string variable;
    public string sign;
    public float value;
    public int pp;

    public JsonBuffNDebuffs() { }

    public JsonBuffNDebuffs(BuffNDebuffs buff)
    {
        name = buff.name;
        variable = buff.variable;
        sign = buff.sign;
        value = buff.value;
        pp = buff.pp;
    }

    public BuffNDebuffs ToBuffNDebuffs(int index)
    {
        BuffNDebuffs buff = new BuffNDebuffs(index);
        buff.name = name;
        buff.variable = variable;
        buff.sign = sign;
        buff.value = value;
        buff.pp = pp;
        return buff;
    }
}

[Serializable]
public class JsonPartyCharacters
{
    public string name;
    public string[] activeCharacterIds; // Store character IDs instead of references
    public string[] stockCharacterIds;

    public JsonPartyCharacters() { }

    public JsonPartyCharacters(string partyName)
    {
        name = partyName;
        activeCharacterIds = new string[4] { null, null, null, null }; // Empty active slots
        stockCharacterIds = new string[12]; // Empty stock slots (default to null)
        for (int i = 0; i < stockCharacterIds.Length; i++)
        {
            stockCharacterIds[i] = null; // null means empty slot
        }
    }

    public JsonPartyCharacters(PartyCharacters party)
    {
        name = party.name;
        activeCharacterIds = new string[party.activeCharacters.Length];
        stockCharacterIds = new string[party.stockCharacters.Length];

        for (int i = 0; i < party.activeCharacters.Length; i++)
        {
            activeCharacterIds[i] = party.activeCharacters[i]?.id;
        }

        for (int i = 0; i < party.stockCharacters.Length; i++)
        {
            stockCharacterIds[i] = party.stockCharacters[i]?.id;
        }
    }

    public PartyCharacters ToPartyCharacters(List<BattleCharacter> allCharacters)
    {
        BattleCharacter[] activeChars = new BattleCharacter[activeCharacterIds.Length];
        BattleCharacter[] stockChars = new BattleCharacter[stockCharacterIds.Length];

        for (int i = 0; i < activeCharacterIds.Length; i++)
        {
            if (!string.IsNullOrEmpty(activeCharacterIds[i]))
            {
                activeChars[i] = allCharacters.Find(c => c.id == activeCharacterIds[i]);
            }
        }

        for (int i = 0; i < stockCharacterIds.Length; i++)
        {
            if (!string.IsNullOrEmpty(stockCharacterIds[i]))
            {
                stockChars[i] = allCharacters.Find(c => c.id == stockCharacterIds[i]);
            }
        }

        return new PartyCharacters(name, activeChars, stockChars);
    }
}

// Container classes for different save files
[Serializable]
public class JsonVolumeSettings
{
    public float mainVolume;
    public float musicVolume;
    public float sfxVolume;
    public bool musicEnabled;
    public bool sfxEnabled;

    public JsonVolumeSettings() { }
}

[Serializable]
public class JsonDamageVariables
{
    public float B, C, D, E, F, G;
    public float Difficulty;
    public float StockWeight;
    public float MaxPP;
    public float ReductionPerCombo;
    public float FractionOfAttacksPerAction;
    public int ModFraction;
    public float IDBOffset;

    public JsonDamageVariables() { }
}

[Serializable]
public class JsonComboChances
{
    public int[] values;

    public JsonComboChances() { }
}

[Serializable]
public class JsonGoldenStrike
{
    public int[] values;

    public JsonGoldenStrike() { }
}

[Serializable]
public class JsonEnergyAmount
{
    public int energyTimer;

    public JsonEnergyAmount() { }
}
