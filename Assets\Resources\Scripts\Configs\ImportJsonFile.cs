using System;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.UI;

public class ImportJsonFile : MonoBehaviour
{
    public Button importButton;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        importButton.onClick.AddListener(OpenFileExplorer);
    }

    // Update is called once per frame
    void Update()
    {

    }

    void OpenFileExplorer()
    {
        NativeFilePicker.PickFile((importPath) =>
        {
            if (!string.IsNullOrEmpty(importPath))
            {
                if (ImportFile(importPath, "characters.json"))
                {
                    new ReloadGame(); // <-- Call here after successful import
                }
            }
            else
            {
                Debug.LogWarning("No file selected.");
            }
        });
    }

    bool ImportFile(string importPath, string targetJsonFile)
    {
        try
        {
            // Only allow replacing characters.json
            if (!string.Equals(targetJsonFile, "characters.json", StringComparison.OrdinalIgnoreCase))
            {
                Debug.LogError("Only characters.json can be replaced by import.");
                return false;
            }

            // Get the save folder path
            string saveFolder = (Application.platform == RuntimePlatform.WindowsEditor || Application.platform == RuntimePlatform.WindowsPlayer)
                ? Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), @"DKGRPGPrototype_unity")
                : Application.persistentDataPath;

            string targetPath = Path.Combine(saveFolder, targetJsonFile);

            // Check if the import file exists
            if (!File.Exists(importPath))
            {
                Debug.LogError($"Import file not found: {importPath}");
                return false;
            }

            // Load current and imported JSON
            var currentJson = JObject.Parse(File.ReadAllText(targetPath));
            var importJson = JObject.Parse(File.ReadAllText(importPath));

            // Find "character" value from imported file where id == "BU0"
            var importedChar = importJson["characterPkg"]?["data"]?.FirstOrDefault(c => c["id"]?.ToString() == "C1");
            if (importedChar == null || importedChar["name"] == null)
            {
                Debug.LogError("No character with id 'BU0' found in import file.");
                return false;
            }
            string importedCharacterValue = importedChar["name"].ToString();

            // Find character with id == 0 in current file and replace its "name"
            var currentChar = currentJson["characters"]?.FirstOrDefault(c =>
                c["id"] != null && (
                    c["id"].Type == JTokenType.Integer && c["id"].ToObject<int>() == 0 ||
                    c["id"].Type == JTokenType.String && c["id"].ToString() == "0"
                )
            );
            if (currentChar == null)
            {
                Debug.LogError("No character with id '0' found in current file.");

                return false;
            }
            currentChar["name"] = importedCharacterValue;

            // Save the modified current file
            File.WriteAllText(targetPath, currentJson.ToString(Formatting.Indented));
            Debug.Log("Name replaced successfully.");
            return true;
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error importing JSON file: {ex.Message}");
            return false;
        }
    }

}
