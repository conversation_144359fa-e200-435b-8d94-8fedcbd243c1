using UnityEngine;
using System;

/// <summary>
/// Test script to verify SkillValues JSON serialization works correctly
/// This script can be attached to a GameObject to test the new SkillValues structure
/// </summary>
public class SkillValuesTest : MonoBehaviour
{
    [Header("Test Results")]
    [SerializeField] private bool testPassed = false;
    [SerializeField] private string testResults = "";

    void Start()
    {
        RunTests();
    }

    public void RunTests()
    {
        Debug.Log("[SkillValuesTest] Starting SkillValues JSON serialization tests...");
        
        try
        {
            // Test 1: Create SkillValues with new constructor
            TestNewConstructor();
            
            // Test 2: Test JSON serialization
            TestJsonSerialization();
            
            // Test 3: Test backward compatibility
            TestBackwardCompatibility();
            
            // Test 4: Test CharacterSkills creation
            TestCharacterSkillsCreation();
            
            testPassed = true;
            testResults = "All tests passed successfully!";
            Debug.Log("[SkillValuesTest] ✅ All tests passed!");
        }
        catch (Exception ex)
        {
            testPassed = false;
            testResults = $"Test failed: {ex.Message}";
            Debug.LogError($"[SkillValuesTest] ❌ Test failed: {ex.Message}");
        }
    }

    private void TestNewConstructor()
    {
        Debug.Log("[SkillValuesTest] Testing new SkillValues constructor...");
        
        SkillValues skillValues = new SkillValues(10, 15, "Strength", "St");
        
        if (skillValues.spDef != 10)
            throw new Exception($"Expected spDef=10, got {skillValues.spDef}");
        if (skillValues.spAtk != 15)
            throw new Exception($"Expected spAtk=15, got {skillValues.spAtk}");
        if (skillValues.type != "Strength")
            throw new Exception($"Expected type='Strength', got '{skillValues.type}'");
        if (skillValues.acronym != "St")
            throw new Exception($"Expected acronym='St', got '{skillValues.acronym}'");
            
        Debug.Log("[SkillValuesTest] ✅ New constructor test passed");
    }

    private void TestJsonSerialization()
    {
        Debug.Log("[SkillValuesTest] Testing JSON serialization...");
        
        // Create a SkillValues object
        SkillValues original = new SkillValues(7, 12, "Magic", "Ma");
        
        // Convert to JSON format
        JsonSkillValues jsonSkillValues = new JsonSkillValues(original);
        
        // Verify JSON object has correct values
        if (jsonSkillValues.spDef != 7)
            throw new Exception($"JSON spDef: Expected 7, got {jsonSkillValues.spDef}");
        if (jsonSkillValues.spAtk != 12)
            throw new Exception($"JSON spAtk: Expected 12, got {jsonSkillValues.spAtk}");
        if (jsonSkillValues.type != "Magic")
            throw new Exception($"JSON type: Expected 'Magic', got '{jsonSkillValues.type}'");
        if (jsonSkillValues.acronym != "Ma")
            throw new Exception($"JSON acronym: Expected 'Ma', got '{jsonSkillValues.acronym}'");
        
        // Convert back to SkillValues
        SkillValues converted = jsonSkillValues.ToSkillValues();
        
        // Verify converted object matches original
        if (converted.spDef != original.spDef)
            throw new Exception($"Converted spDef: Expected {original.spDef}, got {converted.spDef}");
        if (converted.spAtk != original.spAtk)
            throw new Exception($"Converted spAtk: Expected {original.spAtk}, got {converted.spAtk}");
        if (converted.type != original.type)
            throw new Exception($"Converted type: Expected '{original.type}', got '{converted.type}'");
        if (converted.acronym != original.acronym)
            throw new Exception($"Converted acronym: Expected '{original.acronym}', got '{converted.acronym}'");
            
        Debug.Log("[SkillValuesTest] ✅ JSON serialization test passed");
    }

    private void TestBackwardCompatibility()
    {
        Debug.Log("[SkillValuesTest] Testing backward compatibility...");
        
        // Create a JsonSkillValues object without type/acronym (simulating old save data)
        JsonSkillValues oldFormat = new JsonSkillValues();
        oldFormat.spDef = 5;
        oldFormat.spAtk = 8;
        // type and acronym are null/empty (old format)
        
        // Convert to SkillValues - should work with default values
        SkillValues converted = oldFormat.ToSkillValues();
        
        if (converted.spDef != 5)
            throw new Exception($"Backward compatibility spDef: Expected 5, got {converted.spDef}");
        if (converted.spAtk != 8)
            throw new Exception($"Backward compatibility spAtk: Expected 8, got {converted.spAtk}");
        if (converted.type != "")
            throw new Exception($"Backward compatibility type: Expected '', got '{converted.type}'");
        if (converted.acronym != "")
            throw new Exception($"Backward compatibility acronym: Expected '', got '{converted.acronym}'");
            
        Debug.Log("[SkillValuesTest] ✅ Backward compatibility test passed");
    }

    private void TestCharacterSkillsCreation()
    {
        Debug.Log("[SkillValuesTest] Testing CharacterSkills creation...");
        
        // Create a new CharacterSkills object
        CharacterSkills skills = new CharacterSkills();
        
        // Check that all types have been properly initialized
        foreach (Types type in Enum.GetValues(typeof(Types)))
        {
            SkillValues skillValues = skills.GetValues(type);
            
            if (skillValues == null)
                throw new Exception($"SkillValues for type {type} is null");
            if (string.IsNullOrEmpty(skillValues.type))
                throw new Exception($"Type name for {type} is null or empty");
            if (string.IsNullOrEmpty(skillValues.acronym))
                throw new Exception($"Acronym for {type} is null or empty");
            if (skillValues.type != type.ToString())
                throw new Exception($"Type name mismatch: Expected '{type}', got '{skillValues.type}'");
                
            string expectedAcronym = type.ToString().Length >= 2 ? type.ToString()[..2] : type.ToString();
            if (skillValues.acronym != expectedAcronym)
                throw new Exception($"Acronym mismatch for {type}: Expected '{expectedAcronym}', got '{skillValues.acronym}'");
        }
        
        Debug.Log("[SkillValuesTest] ✅ CharacterSkills creation test passed");
    }
}
