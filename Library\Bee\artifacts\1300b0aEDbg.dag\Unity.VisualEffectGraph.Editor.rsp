-target:library
-out:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll"
-refout:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.ref.dll"
-define:UNITY_6000_0_36
-define:UNITY_6000_0
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_ENGINE_CODE_STRIPPING
-define:ENABLE_ONSCREEN_KEYBOARD
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_ACCESSIBILITY
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_ANDROID
-define:UNITY_ANDROID
-define:UNITY_ANDROID_API
-define:ENABLE_EGL
-define:ENABLE_NETWORK
-define:ENABLE_RUNTIME_GI
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:UNITY_CAN_SHOW_SPLASH_SCREEN
-define:UNITY_HAS_GOOGLEVR
-define:UNITY_HAS_TANGO
-define:ENABLE_SPATIALTRACKING
-define:ENABLE_ETC_COMPRESSION
-define:PLATFORM_EXTENDS_VULKAN_DEVICE
-define:PLATFORM_HAS_MULTIPLE_SWAPCHAINS
-define:UNITY_ANDROID_SUPPORTS_SHADOWFILES
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:PLATFORM_EXTENDS_VULKAN_PIPELINE_CACHE
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:PLATFORM_HAS_ADDITIONAL_API_CHECKS
-define:ENABLE_UNITYADS_RUNTIME
-define:UNITY_UNITYADS_API
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:UNITY_POST_PROCESSING_STACK_V2
-define:DOTWEEN
-define:VFX_HAS_TIMELINE
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-define:UNITY_EDITOR_ONLY_COMPILATION
-r:"Assets/Plugins/Demigiant/DOTween/DOTween.dll"
-r:"Assets/Plugins/Demigiant/DOTween/Editor/DOTweenEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.AMDModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.Apple.Extensions.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@001b54a8988a/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@001b54a8988a/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@001b54a8988a/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@001b54a8988a/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@60ef35ffd3cd/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@60ef35ffd3cd/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/PackageCache/jillejr.newtonsoft.json-for-unity@f2c6cb098178/Plugins/Newtonsoft.Json Editor/Newtonsoft.Json.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Compiler/VFXCodeGenerator.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Compiler/VFXExpressionGraph.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Compiler/VFXExpressionMapper.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Compiler/VFXGraphCompiledData.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Compiler/VFXSGInputs.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Compiler/VFXShaderWriter.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Compiler/VFXUniformMapper.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/Element3D.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXBitField.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXColorField.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXControl.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXEnumField.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXEnumValuePopup.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXFlipBookField.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXLabeledField.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXMatrix4x4Field.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXMinMaxSliderField.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXReorderableList.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXSliderField.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXStringField.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXStringFieldProvider.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXStringFieldPushButton.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXTextEditor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXTextEditorField.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXVector2Field.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXVector3Field.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Controls/VFXVector4Field.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Core/VFXConverter.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Core/VFXEnums.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Core/VFXLibrary.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Core/VFXLibraryStringHelper.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Core/VFXSerializer.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Core/VFXSettingAttribute.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Core/VFXSRPBinder.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Data/VFXData.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Data/VFXDataMesh.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Data/VFXDataOutputEvent.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Data/VFXDataParticle.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Data/VFXDataSpawner.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Debug/DotGraphOutput.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Debug/VFXUIDebug.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXAttributeExpression.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXBuiltInExpression.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionAbstract.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionAbstractBoolOperation.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionAbstractFloatOperation.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionAbstractNumericOperation.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionAbstractUintOperation.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionAbstractValues.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionBakeCurve.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionBakeGradient.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionBuffer.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionCamera.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionCast.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionColor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionCombine.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionContext.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionExtractComponent.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionFlow.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionHLSL.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionLoadCameraBuffer.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionLoadTexture.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionMath.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionMesh.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionNoise.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionRandom.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionSampleAttributeMap.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionSampleCameraBuffer.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionSampleCurve.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionSampleGradient.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionSampleSDF.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionSampleTexture2D.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionSampleTexture2DArray.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionSampleTexture3D.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionSampleTextureCube.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionSampleTextureCubeArray.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionSpawnerState.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionStrips.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionTextureDim.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionTextureValues.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Expressions/VFXExpressionTransform.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/FilterPopup/VFXBlockProvider.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/FilterPopup/VFXFilterWindow.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Gizmo/VFXGizmo.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Gizmo/VFXGizmoUtility.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Blackboard/VFXBlackboard.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Blackboard/VFXBlackboardAttributeField.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Blackboard/VFXBlackboardAttributeRow.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Blackboard/VFXBlackboardAttributeView.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Blackboard/VFXBlackboardCategory.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Blackboard/VFXBlackboardField.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Blackboard/VFXBlackboardFieldBase.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Blackboard/VFXBlackboardPropertyView.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Blackboard/VFXBlackboardRow.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/BoundsRecorder/VFXBoundsRecorder.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/BoundsRecorder/VFXBoundsRecorderField.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/BoundsRecorder/VFXBoundsSelector.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Controllers/Controller.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Controllers/IControlledElement.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/3D/Preview3D.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/3D/Rotate3DManipulator.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/Controllers/VFXBlockController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/Controllers/VFXContextController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/Controllers/VFXContextDataAnchorController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/Controllers/VFXDataAnchorController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/Controllers/VFXDataEdgeController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/Controllers/VFXFlowAnchorController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/Controllers/VFXFlowEdgeController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/Controllers/VFXGroupNodeController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/Controllers/VFXNodeController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/Controllers/VFXOperatorAnchorController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/Controllers/VFXOperatorController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/Controllers/VFXParameterController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/Controllers/VFXParameterNodeController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/Controllers/VFXSettingController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/Controllers/VFXSlotContainerController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXBlockUI.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXContextUI.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXDataAnchor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXDataEdge.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXEdgeConnector.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXEdgeDragInfo.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXEditableDataAnchor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXElement.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXFlowAnchor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXFlowEdge.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXGroupNode.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXIconBadge.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXMultiOperatorEdit.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXNodeUI.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXOperatorUI.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXOutputDataAnchor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXParameterUI.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Elements/VFXStickyNote.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Profiling/VFXAnchoredProfilerUI.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Profiling/VFXContextProfilerUI.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Profiling/VFXProfilingBoard.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Profiling/VFXSystemProfilerUI.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/VFXComponentBoard.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/VFXTypeDefinition.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/VFXViewPreference.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/VFXViewWindow.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Controller/VFXGraphUndoCursor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Controller/VFXGraphValidation.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Controller/VFXSystemController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Controller/VFXViewController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Controller/VFXViewControllerExpressions.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Controller/VFXViewControllerUndo.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/DropDownButtonBase.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Properties/BoolPropertyRM.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Properties/ColorPropertyRM.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Properties/CurvePropertyRM.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Properties/GradientPropertyRM.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Properties/HLSLPropertyRM.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Properties/ListPropertiesRM.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Properties/NumericPropertiesRM.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Properties/ObjectPropertyRM.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Properties/PropertyRM.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Properties/SimplePropertiesRM.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Properties/SpaceablePropertiesRM.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Properties/StringPropertyRM.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Properties/Vector2PropertyRM.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Properties/Vector3PropertyRM.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Properties/VectorPropertiesRM.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/Properties/VFXParameterEnumValuePropertyRM.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/VFXAttachPanel.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/VFXCompileDropdownButton.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/VFXConvertSubgraph.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/VFXCopy.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/VFXCopyPasteCommon.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/VFXHelpDropdownButton.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/VFXNodeProvider.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/VFXPaste.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/VFXPicker.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/VFXSaveDropdownButton.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/VFXVCSDropdownButton.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/GraphView/Views/VFXView.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Inspector/AdvancedVisualEffectEditor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Inspector/GizmoController.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Inspector/VFXAssetEditor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Inspector/VFXBlockEditor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Inspector/VFXContextEditor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Inspector/VFXCustomAttributeDescriptorEditor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Inspector/VFXManagerEditor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Inspector/VFXParameterEditor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Inspector/VFXSlotContainerEditor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Inspector/VisualEffectEditor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Manipulators/DownClickable.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Manipulators/SuperCollapser.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Attribute/AttributeFromCurve.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Attribute/AttributeFromMap.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Attribute/AttributeMassFromVolume.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Attribute/CustomAttributeUtility.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/CameraHelper.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Collision/CollisionBase.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Collision/CollisionCone.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Collision/CollisionDepth.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Collision/CollisionOrientedBox.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Collision/CollisionPlane.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Collision/CollisionSDF.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Collision/CollisionShape.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Collision/CollisionSphere.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Color/ColorOverLife.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/FlipBook/FlipbookPlay.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Forces/ConformToSDF.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Forces/ConformToSphere.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Forces/Drag.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Forces/Force.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Forces/ForceHelper.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Forces/Gravity.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Forces/Turbulence.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Forces/VectorFieldForce.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/GPUEvent/TriggerEvent.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/HLSL/CustomHLSL.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/HLSL/HLSLParser.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Orientation/ConnectTarget.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Orientation/Orient.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Output/CameraFade.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Output/SubpixelAA.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Position/PositionBase.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Position/PositionBox.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Position/PositionCircle.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Position/PositionCone.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Position/PositionDepth.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Position/PositionLine.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Position/PositionMesh.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Position/PositionSDF.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Position/PositionSequential.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Position/PositionShape.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Position/PositionSphere.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Position/PositionTorus.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Position/TileWarp.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/SetAttribute.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Size/ScreenSpaceSize.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Spawn/VFXAbstractSpawner.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Spawn/VFXSpawnerBurst.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Spawn/VFXSpawnerBurstOld.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Spawn/VFXSpawnerConstantRate.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Spawn/VFXSpawnerCustomWrapper.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Spawn/VFXSpawnerPeriodicBurst.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Spawn/VFXSpawnerSetAttribute.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Spawn/VFXSpawnerVariableRate.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Update/Age.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Update/AngularEulerIntegration.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Update/BackupOldPosition.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Update/EulerIntegration.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Update/Reap.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Velocity/VelocityBase.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Velocity/VelocityDirection.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Velocity/VelocityRandomize.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Velocity/VelocitySpeed.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Velocity/VelocitySpherical.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/Velocity/VelocityTangent.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/Implementations/VFXBlockUtility.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/VFXBlock.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Blocks/VFXSubgraphBlock.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXBasicCubeOutput.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXBasicEvent.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXBasicGPUEvent.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXBasicInitialize.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXBasicSpawner.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXBasicUpdate.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXComposedParticleOutput.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXComposedParticleStripOutput.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXComposedShading.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXComposedTopology.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXLineOutput.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXLineStripOutput.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXMeshOutput.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXOutputEvent.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXPlanarPrimitiveHelper.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXPlanarPrimitiveOutput.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXPointOutput.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXQuadStripOutput.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/Implementations/VFXStaticMeshOutput.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/IVFXSubRenderer.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/VFXAbstractComposedParticleOutput.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/VFXAbstractParticleOutput.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/VFXAbstractRenderedOutput.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/VFXBlockSubgraphContext.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/VFXContext.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/VFXGlobalSort.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/VFXMultiMeshHelper.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/VFXOutputUpdate.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/VFXSortingUtility.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/VFXSRPSubOutput.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/VFXSubgraphContext.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Contexts/VFXTask.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/CellularNoise.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/CircleAreaDeprecated.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/CollisionAABoxDeprecatedV2.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/CollisionConeDeprecatedV2.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/CollisionCylinderDeprecated.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/CollisionPlaneDeprecatedV2.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/CollisionSDFDeprecated.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/CollisionSphereDeprecated.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/CollisionSphereDeprecatedV2.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/ConeVolumeDeprecated.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/CrossProductDeprecated.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/CylinderVolumeDeprecated.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/GetCustomAttribute.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/GetSpawnCount.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/GPUEventAlways.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/GPUEventOnDie.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/GPUEventRate.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/KillAABoxDeprecatedV2.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/KillSphereDeprecated.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/KillSphereDeprecatedV2.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/NoiseBaseOld.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/PerlinNoise.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/PositionAABoxDeprecatedV2.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/PositionCircleDeprecated.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/PositionCircleDeprecatedV2.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/PositionConeDeprecated.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/PositionConeDeprecatedV2.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/PositionLineDeprecatedV2.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/PositionSDFDeprecated.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/PositionSphereDeprecated.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/PositionSphereDeprecatedV2.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/PositionTorusDeprecated.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/PositionTorusDeprecatedV2.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/SanitizeHelper.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/SetCustomAttribute.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/SimplexNoise.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/SphereVolumeDeprecated.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/TorusVolumeDeprecated.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/ValueNoise.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Deprecated/VFXDecalOutput.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/IVFXAttributesManager.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/AABoxVolume.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Absolute.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Acos.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Add.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/AgeOverLifetime.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/AppendVector.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Asin.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Atan.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Atan2.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/BitwiseAnd.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/BitwiseComplement.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/BitwiseLeftShift.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/BitwiseOr.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/BitwiseRightShift.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/BitwiseXor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Branch.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/BufferCount.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Ceiling.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/ChangeSpace.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/CircleArea.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Clamp.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/ColorLuma.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Condition.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/ConeVolume.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/ConstructMatrix.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Cosine.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/CrossProduct.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/CurlNoise.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/CustomHLSL.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Discretize.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Distance.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/DistanceToLine.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/DistanceToPlane.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/DistanceToSphere.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Divide.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/DotProduct.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Epsilon.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Exp.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Floor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Fractional.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/HSVtoRGB.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/InverseLerp.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/InverseTRSMatrix.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Length.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Lerp.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/LoadCameraBuffer.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/LoadTexture2D.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/LoadTexture2DArray.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/LoadTexture3D.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Log.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/LogicalAnd.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/LogicalNand.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/LogicalNor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/LogicalNot.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/LogicalOr.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/LookAt.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/MainCamera.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Maximum.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/MeshIndexCount.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/MeshTriangleCount.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/MeshVertexCount.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Minimum.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Modulo.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Multiply.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Negate.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Noise.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/NoiseBase.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Normalize.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/OneMinus.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/OrientedBoxVolume.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/PeriodicTotalTime.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/PerParticleTotalTime.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Pi.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/PolarToRectangular.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/PositionDepth.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Power.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Random.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/RandomSelector.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/RatioOverStrip.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Reciprocal.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/RectangularToPolar.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/RectangularToSpherical.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Remap.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/RemapToNegOnePosOne.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/RemapToZeroOne.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/RGBtoHSV.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Rotate2D.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Rotate3D.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Round.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SampleAttributeMap.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SampleBezier.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SampleBuffer.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SampleCameraBuffer.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SampleCurve.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SampleGradient.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SampleIndex.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SampleMesh.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SamplePointCache.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SampleSDF.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SampleTexture2D.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SampleTexture2DArray.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SampleTexture3D.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SampleTextureCube.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SampleTextureCubeArray.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Saturate.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SawtoothWave.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Sequential3D.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SequentialCircle.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SequentialLine.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Sign.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Sine.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SineWave.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SkinnedMeshRendererTransform.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Smoothstep.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SpawnState.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SphereVolume.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SphericalToRectangular.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SplitMatrix.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SquaredDistance.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SquaredLength.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SquareRoot.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/SquareWave.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Step.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Subtract.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Switch.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Swizzle.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/Tangent.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/TextureDimensions.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/TorusVolume.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/TransformDirection.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/TransformMatrix.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/TransformPosition.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/TransformVector.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/TransformVector4.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/TransposeMatrix.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/TriangleWave.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/ViewportToWorldPoint.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/VoroNoise2D.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/Implementations/WorldToViewportPoint.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/VFXAbstractOperatorNew.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/VFXOperator.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/VFXOperatorUtility.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Operators/VFXSubgraphOperator.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Parameters/Deprecated/VFXBuiltInParameter.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Parameters/Deprecated/VFXCurrentAttributeParameter.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Parameters/Deprecated/VFXSourceAttributeParameter.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Parameters/VFXAttributeParameter.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Parameters/VFXDynamicBuiltInParameter.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Parameters/VFXInlineOperator.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Parameters/VFXParameter.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotAnimationCurve.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotBool.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotCameraBuffer.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotColor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotDirection.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotEncapsulated.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotFlipBook.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotFloat.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotFloat2.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotFloat3.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotFloat4.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotGradient.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotGraphicsBuffer.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotInt.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotMatrix4x4.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotMesh.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotObject.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotOrientedBox.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotPosition.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotSkinnedMeshRenderer.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotSphere.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotTexture2D.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotTexture2DArray.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotTexture3D.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotTextureCube.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotTextureCubeArray.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotTransform.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotUint.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/Implementations/VFXSlotVector.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/Slots/VFXSlot.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/VFXAttributesManager.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/VFXCustomAttributeDescriptor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/VFXErrorManager.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/VFXGraph.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/VFXModel.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/VFXParameterInfo.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/VFXSlotContainerModel.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/VFXSystemNames.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Models/VFXUI.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/PackageInfo.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/SamplesLinkPackageManagerExtension.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/ShaderGraph/IRequireVFXContext.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/ShaderGraph/VFXOldShaderGraphHelpers.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/ShaderGraph/VFXShaderGraphHelpers.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/ShaderGraph/VFXShaderGraphParticleOutput.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/ShaderGraph/VFXShaderGraphPostProcessor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/ShaderGraph/VFXSubTarget.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/TemplateWindow/CreateFromTemplateDropDownButton.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/TemplateWindow/VFXTemplateDescriptor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/TemplateWindow/VFXTemplateHelper.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/TemplateWindow/VFXTemplateWindow.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Types/VFXBoxGizmos.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Types/VFXCircleGizmos.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Types/VFXConeGizmos.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Types/VFXLineGizmos.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Types/VFXPlaneGizmos.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Types/VFXProperty.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Types/VFXPropertyAttribute.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Types/VFXSphereGizmos.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Types/VFXTorusGizmos.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Types/VFXTransformGizmos.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Types/VFXTypeExtension.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Types/VFXTypes.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Types/VFXTypesDeprecated.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Types/VFXTypesGizmos.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Types/VFXTypeUtility.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/DotGraph/DotAttribute.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/DotGraph/DotElement.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/DotGraph/DotGraph.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/EventTester/VFXEventTesterWindow.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/ExposedProperty/ExposedPropertyDrawer.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/pCache/BakeTool/PointCacheBakeTool.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/pCache/BakeTool/PointCacheBakeTool.Mesh.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/pCache/BakeTool/PointCacheBakeTool.Texture.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/pCache/Importer/PointCacheImporter.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/pCache/Operator/VFXOperatorPointCache.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/pCache/PCache.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/pCache/PointCache.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/pCache/PointCacheAsset.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/Playables/VisualEffectControlClipEditor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/Playables/VisualEffectControlClipInspector.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/Playables/VisualEffectControlSceneOverlay.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/PropertyBinding/VFXBinderEditor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/PropertyBinding/VFXPropertyBinderEditor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/SDF/BakeTool/SdfBakerPreview.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/SDF/BakeTool/SdfBakerSettings.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/SDF/BakeTool/SDFBakeTool.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/VectorFieldImporter/Editor/VectorFieldImporter.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utilities/VectorFieldImporter/Editor/VectorFieldImporterEditor.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utils/VFXAttributeHelper.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utils/VFXContextBorder.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utils/VFXDebugWindow.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utils/VFXHelpURLAttribute.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utils/VFXResources.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/Utils/VFXSystemBorder.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/VFXAnalytics.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/VFXAssetEditorUtility.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/VisualEffectGraphShortcuts.cs"
"Library/PackageCache/com.unity.visualeffectgraph@ba0fe1b085e9/Editor/VisualElementExtensions.cs"
-langversion:9.0
/deterministic
/optimize-
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.UnityAdditionalFile.txt"