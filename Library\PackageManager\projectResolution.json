{"context": {"projectPath": "D:/<PERSON><PERSON>/DKG-RPG-Mobile/Packages", "unityVersion": "6000.0.36f1"}, "inputs": ["D:\\Menino Autista\\DKG-RPG-Mobile\\Packages\\manifest.json", "D:\\Menino Autista\\DKG-RPG-Mobile\\Packages\\packages-lock.json", "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\BuiltInPackagesCombined.sha1"], "outputs": {"com.unity.collab-proxy@2.6.0": {"name": "com.unity.collab-proxy", "displayName": "Version Control", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.collab-proxy@001b54a8988a", "fingerprint": "001b54a8988a14785c8e209d6c788bb15f9e58cf", "editorCompatibility": "2021.3.0f1", "version": "2.6.0", "source": "registry", "testable": false}, "com.unity.device-simulator.devices@1.0.0": {"name": "com.unity.device-simulator.devices", "displayName": "Device Simulator Devices", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.device-simulator.devices@ae8874e9c898", "fingerprint": "ae8874e9c898e20961be6f426dfafaf49f604cda", "editorCompatibility": "2022.1.0a1", "version": "1.0.0", "source": "registry", "testable": false}, "com.unity.feature.2d@2.0.1": {"name": "com.unity.feature.2d", "displayName": "2D", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.feature.2d@dd1ea8910f12", "fingerprint": "dd1ea8910f12f021c166e8d0d78de44f1390ff6b", "version": "2.0.1", "source": "builtin", "testable": false}, "com.unity.ide.rider@3.0.31": {"name": "com.unity.ide.rider", "displayName": "JetBrains Rider Editor", "resolvedPath": "D:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.ide.rider@7921be93db40", "fingerprint": "7921be93db40ec070fcb01ed82d1c3df1bbdddcd", "editorCompatibility": "2019.2.6f1", "version": "3.0.31", "source": "registry", "testable": false}, "com.unity.ide.visualstudio@2.0.23": {"name": "com.unity.ide.visualstudio", "displayName": "Visual Studio Editor", "resolvedPath": "D:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.ide.visualstudio@198cdf337d13", "fingerprint": "198cdf337d13c83ca953581515630d66b779e92b", "editorCompatibility": "2019.4.25f1", "version": "2.0.23", "source": "registry", "testable": false}, "com.unity.inputsystem@1.12.0": {"name": "com.unity.inputsystem", "displayName": "Input System", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.inputsystem@920b46832575", "fingerprint": "920b46832575a5beecb201e0a4570b4984b7ec34", "editorCompatibility": "2019.4.0a1", "version": "1.12.0", "source": "registry", "testable": false}, "com.unity.multiplayer.center@1.0.0": {"name": "com.unity.multiplayer.center", "displayName": "Multiplayer Center", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.multiplayer.center@f502d8ac613f", "fingerprint": "f502d8ac613fa076192423e73892fbd89eb4049b", "editorCompatibility": "6000.0.0a1", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.postprocessing@3.4.0": {"name": "com.unity.postprocessing", "displayName": "Post Processing", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.postprocessing@026b2a0827a4", "fingerprint": "026b2a0827a438e3079df62a60ca03d54961393d", "editorCompatibility": "2019.4.19f1", "version": "3.4.0", "source": "registry", "testable": false}, "com.unity.render-pipelines.universal@17.0.3": {"name": "com.unity.render-pipelines.universal", "displayName": "Universal RP", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.render-pipelines.universal@a394fe607f89", "fingerprint": "a394fe607f89a478c3cc785b9888af03efac7c84", "editorCompatibility": "6000.0.0a1", "version": "17.0.3", "source": "builtin", "testable": false}, "com.unity.test-framework@1.4.5": {"name": "com.unity.test-framework", "displayName": "Test Framework", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.test-framework@0a21eb82d95c", "fingerprint": "0a21eb82d95cd331643a1e0ce4e8e9a5f18954c8", "editorCompatibility": "2019.4.0a10", "version": "1.4.5", "source": "registry", "testable": false}, "com.unity.timeline@1.8.7": {"name": "com.unity.timeline", "displayName": "Timeline", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.timeline@c58b4ee65782", "fingerprint": "c58b4ee65782ad38338e29f7ee67787cb6998f04", "editorCompatibility": "2019.3.0a1", "version": "1.8.7", "source": "registry", "testable": false}, "com.unity.ugui@2.0.0": {"name": "com.unity.ugui", "displayName": "Unity UI", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.ugui@24b10291b18f", "fingerprint": "24b10291b18f65e937b22d041c48712033f29449", "editorCompatibility": "2019.2.0a1", "version": "2.0.0", "source": "builtin", "testable": false}, "com.unity.visualeffectgraph@17.0.3": {"name": "com.unity.visualeffectgraph", "displayName": "Visual Effect Graph", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9", "fingerprint": "ba0fe1b085e90ce25110d507ce099003cddafca7", "editorCompatibility": "6000.0.0a1", "version": "17.0.3", "source": "builtin", "testable": false}, "com.unity.visualscripting@1.9.5": {"name": "com.unity.visualscripting", "displayName": "Visual Scripting", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b", "fingerprint": "1b53f46e931bea668e53f1feb0ac9138170c9455", "editorCompatibility": "2021.3.0a1", "version": "1.9.5", "source": "registry", "testable": false}, "jillejr.newtonsoft.json-for-unity@https://github.com/jilleJr/Newtonsoft.Json-for-Unity.git#upm": {"name": "jillejr.newtonsoft.json-for-unity", "displayName": "Json.NET 10.0.3 for Unity", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\jillejr.newtonsoft.json-for-unity@f2c6cb098178", "fingerprint": "f2c6cb09817832dccd0600f291053d8d55298ee9", "editorCompatibility": "2018.1.0a1", "version": "10.0.302", "source": "git", "testable": false}, "com.unity.modules.accessibility@1.0.0": {"name": "com.unity.modules.accessibility", "displayName": "Accessibility", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.accessibility", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.ai@1.0.0": {"name": "com.unity.modules.ai", "displayName": "AI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.ai", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.androidjni@1.0.0": {"name": "com.unity.modules.androidjni", "displayName": "Android JNI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.androidjni", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.animation@1.0.0": {"name": "com.unity.modules.animation", "displayName": "Animation", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.animation", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.assetbundle@1.0.0": {"name": "com.unity.modules.assetbundle", "displayName": "<PERSON><PERSON>", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.assetbundle", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.audio@1.0.0": {"name": "com.unity.modules.audio", "displayName": "Audio", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.audio", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.cloth@1.0.0": {"name": "com.unity.modules.cloth", "displayName": "<PERSON><PERSON><PERSON>", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.cloth", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.director@1.0.0": {"name": "com.unity.modules.director", "displayName": "Director", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.director", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.imageconversion@1.0.0": {"name": "com.unity.modules.imageconversion", "displayName": "Image Conversion", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.imageconversion", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.imgui@1.0.0": {"name": "com.unity.modules.imgui", "displayName": "IMGUI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.imgui", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.jsonserialize@1.0.0": {"name": "com.unity.modules.jsonserialize", "displayName": "JSONSerialize", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.jsonserialize", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.particlesystem@1.0.0": {"name": "com.unity.modules.particlesystem", "displayName": "Particle System", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.particlesystem", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.physics@1.0.0": {"name": "com.unity.modules.physics", "displayName": "Physics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.physics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.physics2d@1.0.0": {"name": "com.unity.modules.physics2d", "displayName": "Physics 2D", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.physics2d", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.screencapture@1.0.0": {"name": "com.unity.modules.screencapture", "displayName": "Screen Capture", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.screencapture", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.terrain@1.0.0": {"name": "com.unity.modules.terrain", "displayName": "Terrain", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.terrain", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.terrainphysics@1.0.0": {"name": "com.unity.modules.terrainphysics", "displayName": "Terrain Physics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.terrainphysics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.tilemap@1.0.0": {"name": "com.unity.modules.tilemap", "displayName": "Tilemap", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.tilemap", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.ui@1.0.0": {"name": "com.unity.modules.ui", "displayName": "UI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.ui", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.uielements@1.0.0": {"name": "com.unity.modules.uielements", "displayName": "UIElements", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.uielements", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.umbra@1.0.0": {"name": "com.unity.modules.umbra", "displayName": "Umbra", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.umbra", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unityanalytics@1.0.0": {"name": "com.unity.modules.unityanalytics", "displayName": "Unity Analytics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unityanalytics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequest@1.0.0": {"name": "com.unity.modules.unitywebrequest", "displayName": "Unity Web Request", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequest", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestassetbundle@1.0.0": {"name": "com.unity.modules.unitywebrequestassetbundle", "displayName": "Unity Web Request Asset Bundle", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestassetbundle", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestaudio@1.0.0": {"name": "com.unity.modules.unitywebrequestaudio", "displayName": "Unity Web Request Audio", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestaudio", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequesttexture@1.0.0": {"name": "com.unity.modules.unitywebrequesttexture", "displayName": "Unity Web Request Texture", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequesttexture", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestwww@1.0.0": {"name": "com.unity.modules.unitywebrequestwww", "displayName": "Unity Web Request WWW", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestwww", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.vehicles@1.0.0": {"name": "com.unity.modules.vehicles", "displayName": "Vehicles", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.vehicles", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.video@1.0.0": {"name": "com.unity.modules.video", "displayName": "Video", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.video", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.vr@1.0.0": {"name": "com.unity.modules.vr", "displayName": "VR", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.vr", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.wind@1.0.0": {"name": "com.unity.modules.wind", "displayName": "Wind", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.wind", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.xr@1.0.0": {"name": "com.unity.modules.xr", "displayName": "XR", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.xr", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.subsystems@1.0.0": {"name": "com.unity.modules.subsystems", "displayName": "Subsystems", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.subsystems", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.hierarchycore@1.0.0": {"name": "com.unity.modules.hierarchycore", "displayName": "Hierarchy Core", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.hierarchycore", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.shadergraph@17.0.3": {"name": "com.unity.shadergraph", "displayName": "Shader Graph", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.shadergraph@911117698220", "fingerprint": "9111176982208cf007caa931c3cf2716ceec4632", "editorCompatibility": "6000.0.0a1", "version": "17.0.3", "source": "builtin", "testable": false}, "com.unity.render-pipelines.core@17.0.3": {"name": "com.unity.render-pipelines.core", "displayName": "Core RP Library", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a", "fingerprint": "7bffd5bb179ae35670a1bea73d5805f4510e7add", "editorCompatibility": "6000.0.0a1", "version": "17.0.3", "source": "builtin", "testable": false}, "com.unity.ext.nunit@2.0.5": {"name": "com.unity.ext.nunit", "displayName": "Custom NUnit", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.ext.nunit@60ef35ffd3cd", "fingerprint": "60ef35ffd3cd5e2f5c8887a4a4ca6148854cd092", "editorCompatibility": "2019.4.0a1", "version": "2.0.5", "source": "registry", "testable": false}, "com.unity.render-pipelines.universal-config@17.0.3": {"name": "com.unity.render-pipelines.universal-config", "displayName": "Universal RP Config", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.render-pipelines.universal-config@fa63c96d3e1a", "fingerprint": "fa63c96d3e1af317d84d171cd6c0e0658ab159df", "editorCompatibility": "6000.0.0a1", "version": "17.0.3", "source": "builtin", "testable": false}, "com.unity.2d.animation@10.1.4": {"name": "com.unity.2d.animation", "displayName": "2D Animation", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.2d.animation@494a3b4e73a9", "fingerprint": "494a3b4e73a9ae26677ef6e9fd6bff4ca643770a", "editorCompatibility": "2023.1.0a1", "version": "10.1.4", "source": "registry", "testable": false}, "com.unity.2d.pixel-perfect@5.0.3": {"name": "com.unity.2d.pixel-perfect", "displayName": "2D Pixel Perfect", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.2d.pixel-perfect@e3ae982b672d", "fingerprint": "e3ae982b672dc7cca42a6303bdf53b84c69991da", "editorCompatibility": "2021.1.0a1", "version": "5.0.3", "source": "registry", "testable": false}, "com.unity.2d.psdimporter@9.0.3": {"name": "com.unity.2d.psdimporter", "displayName": "2D PSD Importer", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.2d.psdimporter@676bae148e11", "fingerprint": "676bae148e11de9a02db5a3614b8c56e4f0f44ac", "editorCompatibility": "2023.1.0a1", "version": "9.0.3", "source": "registry", "testable": false}, "com.unity.2d.sprite@1.0.0": {"name": "com.unity.2d.sprite", "displayName": "2D Sprite", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.2d.sprite@288185b75ab4", "fingerprint": "288185b75ab4115633459407db6f453f23b39ae9", "editorCompatibility": "2019.2.0a1", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.2d.spriteshape@10.0.7": {"name": "com.unity.2d.spriteshape", "displayName": "2D SpriteShape", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.2d.spriteshape@9e35352ae135", "fingerprint": "9e35352ae135f602746220e7edc09eb95bbec530", "editorCompatibility": "2023.1.0a1", "version": "10.0.7", "source": "registry", "testable": false}, "com.unity.2d.tilemap@1.0.0": {"name": "com.unity.2d.tilemap", "displayName": "2D Tilemap Editor", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.2d.tilemap@91020cbfae56", "fingerprint": "91020cbfae5609e17fd435cd77f08d0a6b7138fb", "editorCompatibility": "2019.2.0a1", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.2d.tilemap.extras@4.1.0": {"name": "com.unity.2d.tilemap.extras", "displayName": "2D Tilemap Extras", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.2d.tilemap.extras@13634da7dbe0", "fingerprint": "13634da7dbe06c39bac6bbe2d1a166cf91f58ad7", "editorCompatibility": "6000.0.0a1", "version": "4.1.0", "source": "registry", "testable": false}, "com.unity.2d.aseprite@1.1.8": {"name": "com.unity.2d.aseprite", "displayName": "2D Aseprite Importer", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.2d.aseprite@1f731787b516", "fingerprint": "1f731787b516be32a29864ebe53bd4e737058c54", "editorCompatibility": "2021.3.15f1", "version": "1.1.8", "source": "registry", "testable": false}, "com.unity.burst@1.8.18": {"name": "com.unity.burst", "displayName": "<PERSON><PERSON><PERSON>", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.burst@616862665d8c", "fingerprint": "616862665d8c8ffe643d505aebb5de2ee73b0ab0", "editorCompatibility": "2020.3.0a1", "version": "1.8.18", "source": "registry", "testable": false}, "com.unity.mathematics@1.3.2": {"name": "com.unity.mathematics", "displayName": "Mathematics", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.mathematics@8017b507cc74", "fingerprint": "8017b507cc74bf0a1dd14b18aa860569f807314d", "editorCompatibility": "2021.3.0a1", "version": "1.3.2", "source": "registry", "testable": false}, "com.unity.collections@2.5.1": {"name": "com.unity.collections", "displayName": "Collections", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.collections@56bff8827a7e", "fingerprint": "56bff8827a7ef6d44fcee4f36e558a74da89c1a0", "editorCompatibility": "2022.3.11f1", "version": "2.5.1", "source": "registry", "testable": false}, "com.unity.rendering.light-transport@1.0.1": {"name": "com.unity.rendering.light-transport", "displayName": "Unity Light Transport Library", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.rendering.light-transport@307bc27a498f", "fingerprint": "307bc27a498fc9cd409bbd426c85d8dc7f140bc1", "editorCompatibility": "2023.3.0b1", "version": "1.0.1", "source": "builtin", "testable": false}, "com.unity.searcher@4.9.2": {"name": "com.unity.searcher", "displayName": "Searcher", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.searcher@90d011a70418", "fingerprint": "90d011a70418fd5b0f4b8e86df39444d2af228dd", "editorCompatibility": "2019.1.0a1", "version": "4.9.2", "source": "registry", "testable": false}, "com.unity.2d.common@9.0.7": {"name": "com.unity.2d.common", "displayName": "2D Common", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.2d.common@bb1fc9b3d81b", "fingerprint": "bb1fc9b3d81b3bb452c6708e8c088fe4224a0369", "editorCompatibility": "2023.1.0a1", "version": "9.0.7", "source": "registry", "testable": false}, "com.unity.nuget.mono-cecil@1.11.4": {"name": "com.unity.nuget.mono-cecil", "displayName": "Mono Cecil", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.nuget.mono-cecil@d6f9955a5d5f", "fingerprint": "d6f9955a5d5f84d45442ff1ad0fb694cc6e2fd62", "editorCompatibility": "2018.4.0a1", "version": "1.11.4", "source": "registry", "testable": false}, "com.unity.test-framework.performance@3.0.3": {"name": "com.unity.test-framework.performance", "displayName": "Performance testing API", "resolvedPath": "D:\\Menino Autista\\DKG-RPG-Mobile\\Library\\PackageCache\\com.unity.test-framework.performance@fb0dc592af8b", "fingerprint": "fb0dc592af8b524f1a06c50d7caf95552f1bcd2f", "editorCompatibility": "2020.3.0a1", "version": "3.0.3", "source": "registry", "testable": false}}}