using TMPro;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;
using System.Collections.Generic;

public class CharConfUI : MonoBehaviour
{
    public BattleCharacter character; // the character that this UI represents

    private BattleCharacter tempCharacter; // the temporary character to store the values

    readonly float ySpacing = 0.5f; // the spacing between the characters in the UI

    ConfigsHandler configsHandler; // the ConfigsHandler script

    GameObject fatherObject; // the father object of the UI

    GameObject enemy, _name, stats, skills, mods, level, rem; // the game objects of the UI

    GameObject Stats, Skills, Mods; // the game objects of the Stats, Skills and Mods UI

    Button enemyB, statsB, skillsB, modsB, remB; // the buttons of the UI

    TMP_InputField nameI, levelI; // the input fields of the UI

    void Start()
    {

        tempCharacter = character; // sets the temporary character to the character

        fatherObject = GameObject.Find("CharactersInfo"); // gets the father object

        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // gets the ConfigsHandler script

        // gets the Stats, Skills and Mods UI
        Stats = configsHandler.StatsValues;
        Skills = configsHandler.SkillsValues;
        Mods = configsHandler.ModsValues;

        // gets the game objects of the UI
        enemy = transform.GetChild(0).gameObject;
        _name = transform.GetChild(1).gameObject;
        stats = transform.GetChild(2).gameObject;
        skills = transform.GetChild(3).gameObject;
        mods = transform.GetChild(4).gameObject;
        level = transform.GetChild(5).gameObject;
        rem = transform.GetChild(6).gameObject;

        // gets the input of the UI
        enemyB = enemy.GetComponent<Button>();
        nameI = _name.GetComponent<TMP_InputField>();
        statsB = stats.GetComponent<Button>();
        skillsB = skills.GetComponent<Button>();
        modsB = mods.GetComponent<Button>();
        levelI = level.GetComponent<TMP_InputField>();
        remB = rem.GetComponent<Button>();

        // adds the listeners to the buttons
        AddButtonListener(enemyB, ChangeCharacterType);
        AddButtonListener(statsB, ValueButtonClick);
        AddButtonListener(skillsB, ValueButtonClick);
        AddButtonListener(modsB, ValueButtonClick);
        AddButtonListener(remB, RemoveCharacter);

        // adds the listeners to the input fields
        AddTextEditFinishedListener(nameI, ChangeCharacterName);
        AddTextEditFinishedListener(levelI, ChangeCharacterlevel);

        nameI.text = character.name;
        levelI.text = character.level.ToString();
    }

    void Update()
    {
        if (character != null) // if the character is not null update the values
        {
            // gets the most updated value of the character
            if (configsHandler.GetCharacter(character) == -1) character = configsHandler.GetCharacterByID(character.id);

            // sets the position of the UI
            transform.position = new Vector3(fatherObject.transform.position.x, fatherObject.transform.position.y + -ySpacing * configsHandler.GetCharacter(character), 0);

            // changes the color of the enemy button
            enemy.transform.GetChild(0).gameObject.SetActive(character.isEnemy);
        }
        else // otherwise destroy the object
        {
            Destroy(gameObject);
        }
    }

    static void AddTextEditFinishedListener(TMP_InputField inputField, UnityAction<string> listener) // adds the listener to the input field when the input field is deselected
    {
        inputField.onEndEdit.AddListener(listener);
    }

    static void AddButtonListener(Button button, UnityAction<Button> listener) // adds the listener to the button
    {
        button.onClick.AddListener(() => listener(button));
    }

    void ValueButtonClick(Button self) // changes the values of the UI
    {
        var obj = FindObjectsByType<GameObject>(FindObjectsSortMode.None).Where(go => go.name == "CharInfo");
        obj.ToList().ForEach(go => go.SetActive(false));

        // disables the Stats, Skills and Mods UI
        if (Stats != null) Stats.SetActive(false);

        if (Skills != null) Skills.SetActive(false);

        if (Mods != null) Mods.SetActive(false);

        // enables the child GameObject of the button and sets the values 
        self.gameObject.transform.GetChild(0).gameObject.SetActive(true);
        switch (self.gameObject.name)
        {
            case "Stats":
                Stats.SetActive(true);
                Stats.GetComponent<StatsValueDisplay>().SetValues(character, this);
                break;
            case "Skills":
                Skills.SetActive(true);
                Skills.GetComponent<SkillsValueDisplay>().SetValues(character, this);
                break;
            case "Mods":
                Mods.SetActive(true);
                Mods.GetComponent<ModsValueDisplay>().SetValues(character, this);
                break;
        }
    }

    public void RandomizeMods()
    {
        if (GameObject.Find("ModsValues") == null) return;

        CharacterMods charmods = configsHandler.defaultMods[Random.Range(0, configsHandler.defaultMods.Count)];

        ChangeMods("", 0, charmods);
    }

    public void RandomizeSpDefAndAtk() // randomizes the values of the character, only used for the special defense and special attack
    {
        if (GameObject.Find("SkillsValues") == null) return;

        CharacterSkills charskills = configsHandler.defaultSkills[Random.Range(0, configsHandler.defaultSkills.Count)];

        ChangeSPDefAndAtk(Types.Acid, 0, 0, charskills);
    }

    public void RandomizeStats()
    {
        if (GameObject.Find("StatsValues") == null) return;

        CharacterStatus charstats = configsHandler.defaultStats[Random.Range(0, configsHandler.defaultStats.Count)];

        List<int> e = new();

        ChangeStats(e, e, e, e, e, charstats);
    }

    public void ChangeStats(List<int> apmin, List<int> hp, List<int> atk, List<int> def, List<int> atkLim, CharacterStatus stats = null) // changes the stats of the character
    {
        if (stats != null) tempCharacter.stats = stats;
        else
        {
            // sets the values of the temporary character
            tempCharacter.stats.UpdateApMin(apmin);
            tempCharacter.stats.UpdateBl();
            tempCharacter.stats.UpdateHp(hp);
            tempCharacter.stats.UpdateAtk(atk);
            tempCharacter.stats.UpdateDef(def);
            tempCharacter.stats.UpdateAtkLim(atkLim);
        }

        // updates the level to reset it's hp and max hp
        tempCharacter.SetLevel(tempCharacter.level);

        // gets the index of the character in the list and sets the character to the temporary character
        int index = configsHandler.GetCharacter(character);
        configsHandler.SetCharacter(tempCharacter, index);
        character = tempCharacter;

        // after changing the values, updates the Stats UI
        Stats.GetComponent<StatsValueDisplay>().SetValues(character, this);
    }

    public void ChangeSPDefAndAtk(Types type, string spDef, string spAtk, CharacterSkills skills = null) // changes the special defense and special attack of the character
    {
        if (skills != null) tempCharacter.skills = skills;
        else
        {
            string typeName = type.ToString();
            string acronym = typeName.Length >= 2 ? typeName[..2] : typeName;
            tempCharacter.skills.SetValues(type, new SkillValues(spDef ?? "0", spAtk ?? "0", typeName, acronym)); // sets the values of the temporary character
        }
    }

    // Overload method for backward compatibility with int parameters
    public void ChangeSPDefAndAtk(Types type, int spDef, int spAtk, CharacterSkills skills = null)
    {
        ChangeSPDefAndAtk(type, spDef.ToString(), spAtk.ToString(), skills);
    }
        int index = configsHandler.GetCharacter(character); // gets the index of the character in the list
        configsHandler.SetCharacter(tempCharacter, index); // sets the character to the temporary character
        character = tempCharacter;

        ValueButtonClick(skillsB); // updates the Skills UI
    }

    public void ChangeMods(string modName, int value, CharacterMods mods = null) // changes the mods of the character
    {
        if (mods != null) tempCharacter.mods = mods;
        else
            switch (modName.ToLower()) // sets the value of the temporary character
            {
                case "knowledge":
                    tempCharacter.mods.SetKnowledge(value);
                    break;
                case "luck":
                    tempCharacter.mods.SetLuck(value);
                    break;
                case "speed":
                    tempCharacter.mods.SetSpeed(value);
                    break;
                case "precision":
                    tempCharacter.mods.SetPrecision(value);
                    break;
                case "evasion":
                    tempCharacter.mods.SetEvasion(value);
                    break;
                case "criticalchance":
                    tempCharacter.mods.SetCriticalChance(value);
                    break;
            }

        int index = configsHandler.GetCharacter(character); // gets the index of the character in the list
        configsHandler.SetCharacter(tempCharacter, index); // sets the character to the temporary character
        character = tempCharacter;

        ValueButtonClick(modsB); // updates the Mods UI
    }

    void ChangeCharacterType(Button self) // changes the character type
    {
        tempCharacter.isEnemy = !tempCharacter.isEnemy; // inverts the enemy type of the temporary character
        int index = configsHandler.GetCharacter(character); // gets the index of the character in the list
        configsHandler.SetCharacter(tempCharacter, index); // sets the character to the temporary character
        character = tempCharacter;

        if (character.isEnemy) StartCoroutine(configsHandler.SetEnemyCharacter(character)); // if the character is an enemy, sets the enemy character
    }

    void RemoveCharacter(Button self) // removes the character
    {
        configsHandler.RemoveCharacter(character); // removes the character from the list
        character = null; // sets the character to null

        // sets the Stats, Skills and Mods UI to deactivated
        if (Stats != null) Stats.SetActive(false);

        if (Skills != null) Skills.SetActive(false);

        if (Mods != null) Mods.SetActive(false);
    }

    void ChangeCharacterName(string arg) // changes the character name
    {
        tempCharacter.name = nameI.text; // sets the name of the temporary character
        int index = configsHandler.GetCharacter(character); // gets the index of the character in the list

        configsHandler.SetCharacter(tempCharacter, index); // sets the character to the temporary character

        character = tempCharacter;
    }

    void ChangeCharacterlevel(string arg) // changes the character level
    {
        tempCharacter.SetLevel(int.Parse(levelI.text)); // sets the level of the temporary character
        int index = configsHandler.GetCharacter(character); // gets the index of the character in the list
        configsHandler.SetCharacter(tempCharacter, index); // sets the character to the temporary character
        character = tempCharacter;
    }
}
