m_ProjectFiles:
  m_ManifestFileStatus:
    m_FilePath: D:/Menino Autista/DKG-RPG-Mobile/Packages/manifest.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638868796681690139
    m_Hash: 1554714955
  m_LockFileStatus:
    m_FilePath: D:/Menino Autista/DKG-RPG-Mobile/Packages/packages-lock.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638868796682042938
    m_Hash: 3695117048
m_EmbeddedPackageManifests:
  m_ManifestsStatus: {}
m_LocalPackages:
  m_LocalFileStatus: []
m_ProjectPath: D:/Menino Autista/DKG-RPG-Mobile/Packages
m_EditorVersion: 6000.0.36f1 (9fe3b5f71dbb)
m_ResolvedPackages:
- packageId: com.unity.collab-proxy@2.6.0
  testable: 0
  isDirectDependency: 1
  version: 2.6.0
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.collab-proxy@001b54a8988a
  assetPath: Packages/com.unity.collab-proxy
  name: com.unity.collab-proxy
  displayName: Version Control
  author:
    name: 
    email: 
    url: 
  category: Editor
  type: 
  description: The package gives you the ability to use Unity Version Control in
    the Unity editor. To use Unity Version Control, a subscription is required. Learn
    more about how you can get started for free by visiting https://unity.com/solutions/version-control
  errors: []
  versions:
    all:
    - 1.2.3-preview
    - 1.2.4-preview
    - 1.2.6
    - 1.2.7
    - 1.2.9
    - 1.2.11
    - 1.2.15
    - 1.2.16
    - 1.2.17-preview.3
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.9
    - 1.5.7
    - 1.6.0
    - 1.7.1
    - 1.8.0
    - 1.9.0
    - 1.10.2
    - 1.11.2
    - 1.12.5
    - 1.13.5
    - 1.14.1
    - 1.14.4
    - 1.14.7
    - 1.14.9
    - 1.14.12
    - 1.14.13
    - 1.14.15
    - 1.14.16
    - 1.14.17
    - 1.14.18
    - 1.15.1
    - 1.15.4
    - 1.15.7
    - 1.15.9
    - 1.15.12
    - 1.15.13
    - 1.15.15
    - 1.15.16
    - 1.15.17
    - 1.15.18
    - 1.17.0
    - 1.17.1
    - 1.17.2
    - 1.17.6
    - 1.17.7
    - 2.0.0-preview.6
    - 2.0.0-preview.8
    - 2.0.0-preview.15
    - 2.0.0-preview.17
    - 2.0.0-preview.20
    - 2.0.0-preview.21
    - 2.0.0-preview.22
    - 2.0.0
    - 2.0.1
    - 2.0.3
    - 2.0.4
    - 2.0.5
    - 2.0.7
    - 2.1.0-preview.3
    - 2.1.0-preview.5
    - 2.1.0-preview.6
    - 2.1.0
    - 2.2.0
    - 2.3.1
    - 2.4.3
    - 2.4.4
    - 2.5.1
    - 2.5.2
    - 2.6.0
    - 2.7.1
    - 2.8.1
    - 2.8.2
    compatible:
    - 2.6.0
    - 2.7.1
    - 2.8.1
    - 2.8.2
    recommended: 2.6.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - backup
  - cloud
  - collab
  - collaborate
  - collaboration
  - control
  - devops
  - plastic
  - plasticscm
  - source
  - team
  - teams
  - version
  - vcs
  - uvcs
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638684801569990000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.collab-proxy@2.6/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.cloud.collaborate.git
    revision: b7effc6b40a40af218ae30084906471699330ac9
    path: 
  unityLifecycle:
    version: 2.6.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: "{\"changelog\":\"### Added\\n\\n- Added the ability to merge a branch
    or a changeset, using a new context menu actions\\n- Added file conflict & dir
    conflict context menu actions to the merge view\\n- Added diff and merge settings
    in Project Settings -> Version Control -> Unity Version Control Settings\\n-
    Added merge options dialog to the merge view\\n- Added copy path & history context
    menu actions in all menus where they apply\\n- Added an menu entry to 'Open in
    Unity Cloud' showing the repository in the Unity Dashboard\\n\\n### Changed\\n\\n-
    Updated the minimum supported version to Unity 2021.3.0f1\\n- Removed support
    for migrating old Collaborate workspaces to Unity Version Control\\n- Hide from
    the public documentation all internal APIs that were previously visible by mistake\\n-
    Optimized switching operations to only triggers the Package Manager to reinstall
    packages when needed\\n- Optimized incoming changes to only reload the Package
    Manager when needed (Gluon/partial workspace only for now)\\n- Automatically
    add UnityDirMonSyncFile rule for existing ignore.conf to avoid triggering unnecessary
    finding changes operation\\n- Improved the \u201Csearch\u201D edit box so it
    can resize when there is not enough space in the toolbar\\n- Replaced the \\\"D\\\"
    DevOps icon by the branching icon used in the Hub\\n- Replaced the logo of Unity
    in the Sign in to Unity VCS window\\n\\n### Fixed\\n\\n- Fixed files getting
    checked out even though they are in hidden_changes.conf\\n- Fixed manual login
    to Cloud that didn't work with an Enterprise installation\\n- Fixed resolve conflicts
    not informing about lack of UVCS installation\\n- Fixed the Invite users to cloud
    organization when using an Enterprise installation\\n- Fixed new child branch
    not created from HEAD after update\\n- Fixed the learn more (here) link that
    showed the hex color value in Unity 6\\n- Fixed link to invite members in Unity
    Cloud.\\n- Fixed a crash in the create workspace window when unable to resolve
    a @unity organization\\n- Fixed incoming changes view that was not kept as selected
    after resolving some conflicts\\n- Fixed workspace name that was not refreshed
    after repository manual creation or selection\\n- Added a warning message to
    inform users about mismatching cloud project.\\n- Removed from the Create Workspace
    window the Local server that was present with no installation of UVCS\\n- Replaced
    hardcoded urls pointing to plasticscm.com\"}"
  assetStore:
    productId: 
  fingerprint: 001b54a8988a14785c8e209d6c788bb15f9e58cf
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.6.0
    minimumUnityVersion: 2021.3.0f1
- packageId: com.unity.device-simulator.devices@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.device-simulator.devices@ae8874e9c898
  assetPath: Packages/com.unity.device-simulator.devices
  name: com.unity.device-simulator.devices
  displayName: Device Simulator Devices
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: This package contains additional devices for the Device Simulator.
    Each device has a complete device definition and includes a device overlay. After
    you install this package, the additional devices are immediately available in
    the Device Simulator.
  errors: []
  versions:
    all:
    - 1.0.0-pre.1
    - 1.0.0-pre.2
    - 1.0.0-pre.4
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637810555200000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.device-simulator.devices.git
    revision: 2e1ffaaea0624daf91ee6c76906fd119dbd584c6
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: ae8874e9c898e20961be6f426dfafaf49f604cda
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 2022.1.0a1
- packageId: com.unity.feature.2d@2.0.1
  testable: 0
  isDirectDependency: 1
  version: 2.0.1
  source: 2
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.feature.2d@dd1ea8910f12
  assetPath: Packages/com.unity.feature.2d
  name: com.unity.feature.2d
  displayName: 2D
  author:
    name: 
    email: 
    url: 
  category: 
  type: feature
  description: Import images including multi-layered Photoshop files as Sprites and
    configure them to create 2D games. Create freeform, tile-based and spline-based
    2D game worlds. Create frame-by-frame and bone-based animated characters. Integrated
    with 2D physics to support simulations with colliders and joints. Supports the
    needs of a range of 2D art styles, including pixel art.
  errors: []
  versions:
    all:
    - 2.0.1
    compatible:
    - 2.0.1
    recommended: 2.0.1
    deprecated: []
  dependencies:
  - name: com.unity.2d.animation
    version: default
  - name: com.unity.2d.pixel-perfect
    version: default
  - name: com.unity.2d.psdimporter
    version: default
  - name: com.unity.2d.sprite
    version: default
  - name: com.unity.2d.spriteshape
    version: default
  - name: com.unity.2d.tilemap
    version: default
  - name: com.unity.2d.tilemap.extras
    version: default
  - name: com.unity.2d.aseprite
    version: default
  resolvedDependencies:
  - name: com.unity.2d.animation
    version: 10.1.4
  - name: com.unity.2d.common
    version: 9.0.7
  - name: com.unity.burst
    version: 1.8.18
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  - name: com.unity.2d.pixel-perfect
    version: 5.0.3
  - name: com.unity.2d.psdimporter
    version: 9.0.3
  - name: com.unity.2d.spriteshape
    version: 10.0.7
  - name: com.unity.modules.physics2d
    version: 1.0.0
  - name: com.unity.2d.tilemap
    version: 1.0.0
  - name: com.unity.modules.tilemap
    version: 1.0.0
  - name: com.unity.2d.tilemap.extras
    version: 4.1.0
  - name: com.unity.2d.aseprite
    version: 1.1.8
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 2.0.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"quickstart":"https://docs.unity3d.com/Documentation/Manual/2DFeature.html"}'
  assetStore:
    productId: 
  fingerprint: dd1ea8910f12f021c166e8d0d78de44f1390ff6b
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.1
    minimumUnityVersion: 
- packageId: com.unity.ide.rider@3.0.31
  testable: 0
  isDirectDependency: 1
  version: 3.0.31
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.ide.rider@7921be93db40
  assetPath: Packages/com.unity.ide.rider
  name: com.unity.ide.rider
  displayName: JetBrains Rider Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: The JetBrains Rider Editor package provides an integration for using
    the JetBrains Rider IDE as a code editor for Unity. It adds support for generating
    .csproj files for code completion and auto-discovery of installations.
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.6
    - 1.0.8
    - 1.1.0
    - 1.1.1
    - 1.1.2-preview
    - 1.1.2-preview.2
    - 1.1.3-preview.1
    - 1.1.4-preview
    - 1.1.4
    - 1.2.0-preview
    - 1.2.1
    - 2.0.0-preview
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.5
    - 2.0.7
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.7
    - 3.0.9
    - 3.0.10
    - 3.0.12
    - 3.0.13
    - 3.0.14
    - 3.0.15
    - 3.0.16
    - 3.0.17
    - 3.0.18
    - 3.0.20
    - 3.0.21
    - 3.0.22
    - 3.0.24
    - 3.0.25
    - 3.0.26
    - 3.0.27
    - 3.0.28
    - 3.0.31
    - 3.0.34
    - 3.0.35
    - 3.0.36
    compatible:
    - 3.0.31
    - 3.0.34
    - 3.0.35
    - 3.0.36
    recommended: 3.0.36
    deprecated: []
  dependencies:
  - name: com.unity.ext.nunit
    version: 1.0.6
  resolvedDependencies:
  - name: com.unity.ext.nunit
    version: 2.0.5
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638548308080000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.rider@3.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.rider.git
    revision: ccd778a2275ff09f1a83bc924cfb2fb4d5b63566
    path: 
  unityLifecycle:
    version: 3.0.31
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"fix RIDER-104519 Rider is reporting errors in scripts
    that work fine in Unity when utilizing DOTS - when Player project, by generating
    projects for all assemblies in \"com.unity.entities\", \"com.unity.collections\"\nfix
    RIDER-111622 Unity Rider package is not compatible with Rider Dev builds"}'
  assetStore:
    productId: 
  fingerprint: 7921be93db40ec070fcb01ed82d1c3df1bbdddcd
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.0.31
    minimumUnityVersion: 2019.2.6f1
- packageId: com.unity.ide.visualstudio@2.0.23
  testable: 0
  isDirectDependency: 1
  version: 2.0.23
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.ide.visualstudio@198cdf337d13
  assetPath: Packages/com.unity.ide.visualstudio
  name: com.unity.ide.visualstudio
  displayName: Visual Studio Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Code editor integration for supporting Visual Studio as code editor
    for unity. Adds support for generating csproj files for intellisense purposes,
    auto discovery of installations, etc.
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.3
    - 1.0.4
    - 1.0.9
    - 1.0.10
    - 1.0.11
    - 2.0.0
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.5
    - 2.0.7
    - 2.0.8
    - 2.0.9
    - 2.0.11
    - 2.0.12
    - 2.0.14
    - 2.0.15
    - 2.0.16
    - 2.0.17
    - 2.0.18
    - 2.0.20
    - 2.0.21
    - 2.0.22
    - 2.0.23
    compatible:
    - 2.0.22
    - 2.0.23
    recommended: 2.0.23
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.9
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638786696173840000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.visualstudio@2.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.visualstudio.git
    revision: 0fe3b29f9aff2b90b9f0962ae35036a824d3dd6b
    path: 
  unityLifecycle:
    version: 2.0.22
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"Integration:\n\n- Monitor `additionalfile` extension
    by default.\n- Try opening a Visual Studio Code workspace if there''s one (`.code-workspace`
    file in the Unity project).\n\nProject generation:\n\n- Identify `asset`, `meta`,
    `prefab` and `unity` files as `yaml` (Visual Studio Code).\n- Add `sln`/`csproj`
    file nesting (Visual Studio Code).\n- Improve SDK style project generation."}'
  assetStore:
    productId: 
  fingerprint: 198cdf337d13c83ca953581515630d66b779e92b
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.22
    minimumUnityVersion: 2019.4.25f1
- packageId: com.unity.inputsystem@1.12.0
  testable: 0
  isDirectDependency: 1
  version: 1.12.0
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.inputsystem@920b46832575
  assetPath: Packages/com.unity.inputsystem
  name: com.unity.inputsystem
  displayName: Input System
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: A new input system which can be used as a more extensible and customizable
    alternative to Unity's classic input system in UnityEngine.Input.
  errors: []
  versions:
    all:
    - 0.1.2-preview
    - 0.2.0-preview
    - 0.2.1-preview
    - 0.2.6-preview
    - 0.2.8-preview
    - 0.2.10-preview
    - 0.9.0-preview
    - 0.9.1-preview
    - 0.9.2-preview
    - 0.9.3-preview
    - 0.9.4-preview
    - 0.9.5-preview
    - 0.9.6-preview
    - 1.0.0-preview
    - 1.0.0-preview.1
    - 1.0.0-preview.2
    - 1.0.0-preview.3
    - 1.0.0-preview.4
    - 1.0.0-preview.5
    - 1.0.0-preview.6
    - 1.0.0-preview.7
    - 1.0.0
    - 1.0.1
    - 1.0.2
    - 1.1.0-pre.5
    - 1.1.0-pre.6
    - 1.1.0-preview.1
    - 1.1.0-preview.2
    - 1.1.0-preview.3
    - 1.1.1
    - 1.2.0
    - 1.3.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.5.0
    - 1.5.1
    - 1.6.1
    - 1.6.3
    - 1.7.0
    - 1.8.0-pre.1
    - 1.8.0-pre.2
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.9.0
    - 1.10.0
    - 1.11.0
    - 1.11.1
    - 1.11.2
    - 1.12.0
    - 1.13.0
    - 1.13.1
    - 1.14.0
    compatible:
    - 1.12.0
    - 1.13.0
    - 1.13.1
    - 1.14.0
    recommended: 1.12.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - input
  - events
  - keyboard
  - mouse
  - gamepad
  - touch
  - vr
  - xr
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638726197023440000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.inputsystem@1.12/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/InputSystem.git
    revision: 98646c95066dabdc4cb605460a32213c2c078e46
    path: 
  unityLifecycle:
    version: 1.12.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Fixed an issue causing the Action context
    menu to not show on right click when right clicking an action in the Input Action
    Editor [ISXB-1134](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1134).\n-
    Reverted changes from 0ddd534d8 (ISXB-746) which introduced a regression [ISXB-1127](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1127).\n-
    Fixed `ArgumentNullException: Value cannot be null.` during the migration of
    Project-wide Input Actions from `InputManager.asset` to `InputSystem_Actions.inputactions`
    asset which lead do the lost of the configuration [ISXB-1105](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1105).\n-
    Fixed pointerId staying the same when simultaneously releasing and then pressing
    in the same frame on mobile using touch. [ISXB-1006](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-845).\n-
    Fixed ISubmitHandler.OnSubmit event processing when operating in Manual Update
    mode (ISXB-1141).\n- Fixed Rename mode is not entered and name is autocompleted
    to default when creating a new Action Map on 2022.3. [ISXB-1151](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1151).\n-
    Fixed unexpected control scheme switch when using `OnScreenControl` and pointer
    based schemes which registed \"Cancel\" event on every frame.[ISXB-656](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-656).\n-
    Fixed an issue with The \"Add Control Scheme...\" popup window so that it now
    persists until any changes are explicitly Saved or Cancelled [case ISXB-1131](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1131).\n-
    Fixed missing documentation for source generated Input Action Assets. This is
    now generated as part of the source code generation step when \"Generate C# Class\"
    is checked in the importer inspector settings.\n- Fixed pasting into an empty
    map list raising an exception. [ISXB-1150](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1150)\n-
    Fixed pasting bindings into empty Input Action asset. [ISXB-1180](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1180)\n-
    Fixed missing ''&'' symbol in Control Scheme dropdown on Windows platform. [ISXB-1109](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1109)\n-
    Fixed icon scaling in Input Actions window.\n- Fixed an issue where removing
    the InputSystem package could lead to invalid input handling settings.\n- Fixed
    `ArgumentOutOfRangeException` when adding a new Control Scheme with any Device
    selected. [ISXB-1129](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1129)\n-
    Fixed a CS0105 compiler warning due to duplicate using statement in test source
    code (ISXB-1247).\n- Fixed tooltip support in the UI Toolkit version of the Input
    Actions Asset editor.\n- Fixed documentation to clarify bindings with modifiers
    `overrideModifiersNeedToBePressedFirst` configuration [ISXB-806](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-806).\n-
    Fixed an issue in `Samples/Visualizers/GamepadVisualizer.unity` sample where
    the visualization wouldn''t handle device disconnects or current device changes
    properly (ISXB-1243).\n- Fixed an issue when displaying Serialized InputAction''s
    Processor properties inside the Inspector window. [ISXB-1269](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1269)\n-
    Fixed an issue with default device selection when adding new Control Scheme.\n-
    Fixed an issue where action map delegates were not updated when the asset already
    assigned to the PlayerInput component were changed [ISXB-711](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-711).\n-
    Fixed Action properties edition in the UI Toolkit version of the Input Actions
    Asset editor. [ISXB-1277](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1277)\n-
    Fixed an editor crash caused by input debugger device state window reusing cached
    state when reconnecting Stadia controller. [ISXB-658](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-658)\n-
    Fixed an issue where batch jobs would fail with \"Error:"}'
  assetStore:
    productId: 
  fingerprint: 920b46832575a5beecb201e0a4570b4984b7ec34
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.12.0
    minimumUnityVersion: 2019.4.0a1
- packageId: com.unity.multiplayer.center@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.multiplayer.center@f502d8ac613f
  assetPath: Packages/com.unity.multiplayer.center
  name: com.unity.multiplayer.center
  displayName: Multiplayer Center
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The multiplayer center provides a starting point to create multiplayer
    games. It will recommend specific packages and enable you to easily access integrations,
    samples and documentation.
  errors: []
  versions:
    all:
    - 0.2.1
    - 0.3.0
    - 0.4.0
    - 1.0.0-pre.1
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - Multiplayer
  - Netcode
  - Services
  - Tools
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: f502d8ac613fa076192423e73892fbd89eb4049b
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.postprocessing@3.4.0
  testable: 0
  isDirectDependency: 1
  version: 3.4.0
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.postprocessing@026b2a0827a4
  assetPath: Packages/com.unity.postprocessing
  name: com.unity.postprocessing
  displayName: Post Processing
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: The post-processing stack (v2) comes with a collection of effects
    and image filters you can apply to your cameras to improve the visuals of your
    games.
  errors: []
  versions:
    all:
    - 0.1.7
    - 0.1.8
    - 2.0.0-beta
    - 2.0.1-beta
    - 2.0.2-preview
    - 2.0.3-preview
    - 2.0.5-preview
    - 2.0.7-preview
    - 2.0.8-preview
    - 2.0.9-preview
    - 2.0.10-preview
    - 2.0.11-preview
    - 2.0.12-preview
    - 2.0.13-preview
    - 2.0.14-preview
    - 2.0.15-preview
    - 2.0.16-preview
    - 2.0.17-preview
    - 2.1.0
    - 2.1.1
    - 2.1.2
    - 2.1.3
    - 2.1.4
    - 2.1.5
    - 2.1.6
    - 2.1.7
    - 2.2.1
    - 2.2.2
    - 2.3.0
    - 3.0.1
    - 3.0.3
    - 3.1.0
    - 3.1.1
    - 3.2.0
    - 3.2.1
    - 3.2.2
    - 3.3.0
    - 3.4.0
    - 3.5.0
    compatible:
    - 3.4.0
    - 3.5.0
    recommended: 3.4.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638384897060000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.postprocessing@3.4/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/Graphics.git
    revision: 54427d2529ad1a9e8f31c693d751c6371f914e40
    path: 
  unityLifecycle:
    version: 3.4.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n- Added WebGPU support\n\n### Fixed\n- Fixed
    obsolete FormatUsage error\n- Fixed non-scalar logical operation error\n- Fixed
    MSVO to support platforms with limited storage texture support\n- Fixed compute
    based effects not supported on WebGL and Android OpenGL (IN-2999)\n- Fixed grid
    gizmo is visible through geometry when Post Process Layer is enabled (IN-10318)\n-
    Fixed transparent objects rendering incorrectly when TAA is disabled (IN-31494)"}'
  assetStore:
    productId: 
  fingerprint: 026b2a0827a438e3079df62a60ca03d54961393d
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.4.0
    minimumUnityVersion: 2019.4.19f1
- packageId: com.unity.render-pipelines.universal@17.0.3
  testable: 0
  isDirectDependency: 1
  version: 17.0.3
  source: 2
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.render-pipelines.universal@a394fe607f89
  assetPath: Packages/com.unity.render-pipelines.universal
  name: com.unity.render-pipelines.universal
  displayName: Universal RP
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Universal Render Pipeline (URP) is a prebuilt Scriptable Render
    Pipeline, made by Unity. URP provides artist-friendly workflows that let you
    quickly and easily create optimized graphics across a range of platforms, from
    mobile to high-end consoles and PCs.
  errors: []
  versions:
    all:
    - 7.0.0
    - 7.0.1
    - 7.1.1
    - 7.1.2
    - 7.1.5
    - 7.1.6
    - 7.1.7
    - 7.1.8
    - 7.2.0
    - 7.2.1
    - 7.3.1
    - 7.4.1
    - 7.4.2
    - 7.4.3
    - 7.5.1
    - 7.5.2
    - 7.5.3
    - 7.6.0
    - 7.7.0
    - 7.7.1
    - 8.0.1
    - 8.1.0
    - 8.2.0
    - 8.3.1
    - 9.0.0-preview.14
    - 9.0.0-preview.35
    - 9.0.0-preview.55
    - 9.0.0-preview.72
    - 10.0.0-preview.26
    - 10.1.0
    - 10.2.0
    - 10.2.1
    - 10.2.2
    - 10.3.1
    - 10.3.2
    - 10.4.0
    - 10.5.0
    - 10.5.1
    - 10.6.0
    - 10.7.0
    - 10.8.0
    - 10.8.1
    - 10.9.0
    - 10.10.0
    - 10.10.1
    - 17.0.3
    compatible:
    - 17.0.3
    recommended: 17.0.3
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.3
  - name: com.unity.shadergraph
    version: 17.0.3
  - name: com.unity.render-pipelines.universal-config
    version: 17.0.3
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.3
  - name: com.unity.burst
    version: 1.8.18
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  - name: com.unity.shadergraph
    version: 17.0.3
  - name: com.unity.searcher
    version: 4.9.2
  - name: com.unity.render-pipelines.universal-config
    version: 17.0.3
  keywords:
  - graphics
  - performance
  - rendering
  - mobile
  - render
  - pipeline
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.0.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: a394fe607f89a478c3cc785b9888af03efac7c84
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.0.3
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.test-framework@1.4.5
  testable: 0
  isDirectDependency: 1
  version: 1.4.5
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.test-framework@0a21eb82d95c
  assetPath: Packages/com.unity.test-framework
  name: com.unity.test-framework
  displayName: Test Framework
  author:
    name: 
    email: 
    url: 
  category: Unity Test Framework
  type: assets
  description: Test framework for running Edit mode and Play mode tests in Unity.
  errors: []
  versions:
    all:
    - 0.0.4-preview
    - 0.0.29-preview
    - 1.0.0
    - 1.0.7
    - 1.0.9
    - 1.0.11
    - 1.0.12
    - 1.0.13
    - 1.0.14
    - 1.0.16
    - 1.0.17
    - 1.0.18
    - 1.1.0
    - 1.1.1
    - 1.1.2
    - 1.1.3
    - 1.1.5
    - 1.1.8
    - 1.1.9
    - 1.1.11
    - 1.1.13
    - 1.1.14
    - 1.1.16
    - 1.1.18
    - 1.1.19
    - 1.1.20
    - 1.1.22
    - 1.1.24
    - 1.1.26
    - 1.1.27
    - 1.1.29
    - 1.1.30
    - 1.1.31
    - 1.1.33
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 2.0.1-exp.1
    - 2.0.1-exp.2
    - 2.0.1-pre.12
    - 2.0.1-pre.18
    compatible:
    - 1.4.5
    - 1.4.6
    - 2.0.1-exp.1
    - 2.0.1-exp.2
    - 2.0.1-pre.12
    - 2.0.1-pre.18
    recommended: 1.4.6
    deprecated: []
  dependencies:
  - name: com.unity.ext.nunit
    version: 2.0.3
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - Test
  - TestFramework
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638575054880000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.test-framework@1.4/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.test-framework.git
    revision: e50747c90fb18ab9f4f350b60d0fcdcae19bdebc
    path: 
  unityLifecycle:
    version: 1.4.5
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Fixed an issue where batchmode test runs would never
    finish if a test yielded WaitForEndOfFrame (DSTR-1009).\n- Fixed an issue where
    the location prompt was required when using the Install all tests in ''build''
    folder option during test builds.\n- Canceling a PlayMode test run now correctly
    restores the scene setup, instead of leaving the editor in the test scene.\n-
    Fixed an issue where UnitySetUp did not fail when nested coroutines threw an
    exception (DSTR-1007).\n- When selecting multiple tests and running them, the
    test runner now correctly updates the details of the first selected test (UTF-602).\n-
    The interaction mode and application idle time settings is now being changes
    when running tests, resulting in faster test runs if not already in use (applies
    to 2020.3 and later) (DSTR-690).\n- Fixed an issue where some NUnit attributes
    caused errors and stopped async test runs (DSTR-1040).\n- Added support for the
    MaxTime attribute on async and UnityTest methods (DSTR-1040).\n- Fixed a memory
    leak issue where a large number of domain reloads within the same test could
    crash the editor (DSTR-1023).\n- Changed to use a progress bar inside the test
    runner window when running tests. This ensures that the progress bar is not fighting
    to display when e.g. compiling scripts and also makes it easier to cancel a run.
    This progress bar is displayed for all types of runs (EditMode, PlayMode and
    Player). (UTF-596).\n- Fixed an issue where ignored tests with an attributes
    did not display the ignore reason in the test runner UI.\n- Having multiple tests
    with the same unique id no longer gives a error with ''An item with the same
    key has already been added'', but instead logs an warning about the duplicate
    id.\n- The result icons for test suites should no longer flicker when running
    tests.\n- Ensured that test results ignored in the editor but run on a player
    are not overwritten with ignore status when shown in the UI (DSTR-1042).\n- Fixed
    an issue where the RunStarted event was not invoked correctly during a test run
    (DSTR-1046).\n- Fixed an issue where TestStarted and TestFinished events were
    repeated for ignored test fixtures after a domain reload (DSTR-986)."}'
  assetStore:
    productId: 
  fingerprint: 0a21eb82d95cd331643a1e0ce4e8e9a5f18954c8
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.4.5
    minimumUnityVersion: 2019.4.0a10
- packageId: com.unity.timeline@1.8.7
  testable: 0
  isDirectDependency: 1
  version: 1.8.7
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.timeline@c58b4ee65782
  assetPath: Packages/com.unity.timeline
  name: com.unity.timeline
  displayName: Timeline
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Use Unity Timeline to create cinematic content, game-play sequences,
    audio sequences, and complex particle effects.
  errors: []
  versions:
    all:
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    - 1.2.6
    - 1.2.7
    - 1.2.9
    - 1.2.10
    - 1.2.11
    - 1.2.12
    - 1.2.13
    - 1.2.14
    - 1.2.15
    - 1.2.16
    - 1.2.17
    - 1.2.18
    - 1.3.0-preview.2
    - 1.3.0-preview.3
    - 1.3.0-preview.5
    - 1.3.0-preview.6
    - 1.3.0-preview.7
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.4.0-preview.1
    - 1.4.0-preview.2
    - 1.4.0-preview.3
    - 1.4.0-preview.5
    - 1.4.0-preview.6
    - 1.4.0-preview.7
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.4.7
    - 1.4.8
    - 1.5.0-pre.2
    - 1.5.0-preview.1
    - 1.5.0-preview.2
    - 1.5.0-preview.3
    - 1.5.0-preview.4
    - 1.5.0-preview.5
    - 1.5.1-pre.1
    - 1.5.1-pre.2
    - 1.5.1-pre.3
    - 1.5.2
    - 1.5.4
    - 1.5.5
    - 1.5.6
    - 1.5.7
    - 1.6.0-pre.1
    - 1.6.0-pre.3
    - 1.6.0-pre.4
    - 1.6.0-pre.5
    - 1.6.1
    - 1.6.2
    - 1.6.3
    - 1.6.4
    - 1.6.5
    - 1.7.0-pre.1
    - 1.7.0-pre.2
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.7.5
    - 1.7.6
    - 1.7.7
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.5
    - 1.8.6
    - 1.8.7
    - 1.8.8
    compatible:
    - 1.8.7
    - 1.8.8
    recommended: 1.8.8
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  keywords:
  - unity
  - animation
  - editor
  - timeline
  - tools
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638524833900000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.timeline@1.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.timeline.git
    revision: d6432ca638481c3d0e4c01f87f9cdbe3c4b1d529
    path: 
  unityLifecycle:
    version: 1.8.7
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n\n- Released ronl-workflow-custom-marker.md
    Added a new workflow to the Timeline Workflows documentation:\n- Released ronl-workflow-custom-marker.md
    The `Create a custom Notes marker` workflow demonstrates how to create a custom
    marker for adding notes to Timeline instances. This workflow also demonstrates
    how to change the default appearance of a custom marker with scripting and a
    Unity Style Sheet (USS).\n\n### Fixed\n\n- Fixed bug where using , and . (<>)
    to step frames in the Animation Window while the Timeline Window was linked would
    sometimes not work. [IN-56667](https://unity3d.atlassian.net/servicedesk/customer/portal/2/IN-56667)\n-
    When the Timeline and Animation windows are linked and the Timeline Window is
    active, moving the playhead in the Timeline Window will cause the animation window
    to repaint immediately."}'
  assetStore:
    productId: 
  fingerprint: c58b4ee65782ad38338e29f7ee67787cb6998f04
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.8.7
    minimumUnityVersion: 2019.3.0a1
- packageId: com.unity.ugui@2.0.0
  testable: 0
  isDirectDependency: 1
  version: 2.0.0
  source: 2
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.ugui@24b10291b18f
  assetPath: Packages/com.unity.ugui
  name: com.unity.ugui
  displayName: Unity UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: "Unity UI is a set of tools for developing user interfaces for games
    and applications. It is a GameObject-based UI system that uses Components and
    the Game View to arrange, position, and style user interfaces. \u200B You cannot
    use Unity UI to create or change user interfaces in the Unity Editor."
  errors: []
  versions:
    all:
    - 2.0.0
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
    compatible:
    - 2.0.0
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
    recommended: 2.0.0
    deprecated:
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  keywords:
  - UI
  - ugui
  - Unity UI
  - Canvas
  - TextMeshPro
  - TextMesh Pro
  - Text
  - TMP
  - SDF
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 2.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 24b10291b18f65e937b22d041c48712033f29449
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.0
    minimumUnityVersion: 2019.2.0a1
- packageId: com.unity.visualeffectgraph@17.0.3
  testable: 0
  isDirectDependency: 1
  version: 17.0.3
  source: 2
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.visualeffectgraph@ba0fe1b085e9
  assetPath: Packages/com.unity.visualeffectgraph
  name: com.unity.visualeffectgraph
  displayName: Visual Effect Graph
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Visual Effect Graph is a node based visual effect editor. It allows
    you to author next generation visual effects that Unity simulates directly on
    the GPU. The Visual Effect Graph is production-ready for the High Definition
    Render Pipeline and runs on all platforms supported by it. Full support for the
    Universal Render Pipeline and compatible mobile devices is still in development.
  errors: []
  versions:
    all:
    - 4.3.0-preview
    - 4.6.0-preview
    - 4.8.0-preview
    - 4.9.0-preview
    - 4.10.0-preview
    - 5.2.0-preview
    - 5.2.1-preview
    - 5.2.3-preview
    - 5.3.1-preview
    - 5.6.1-preview
    - 5.7.2-preview
    - 5.8.2-preview
    - 5.10.0-preview
    - 5.13.0-preview
    - 5.16.1-preview
    - 6.5.2-preview
    - 6.5.3-preview
    - 6.7.1-preview
    - 6.9.0-preview
    - 6.9.1-preview
    - 6.9.2-preview
    - 7.0.0
    - 7.0.1
    - 7.1.1
    - 7.1.2
    - 7.1.5
    - 7.1.6
    - 7.1.7
    - 7.1.8
    - 7.2.0
    - 7.2.1
    - 7.3.1
    - 7.4.1
    - 7.4.2
    - 7.4.3
    - 7.5.1
    - 7.5.2
    - 7.5.3
    - 7.6.0
    - 7.7.0
    - 7.7.1
    - 8.0.1
    - 8.1.0
    - 8.2.0
    - 8.3.1
    - 9.0.0-preview.13
    - 9.0.0-preview.33
    - 9.0.0-preview.54
    - 9.0.0-preview.71
    - 10.0.0-preview.27
    - 10.1.0
    - 10.2.0
    - 10.2.1
    - 10.2.2
    - 10.3.1
    - 10.3.2
    - 10.4.0
    - 10.5.0
    - 10.5.1
    - 10.6.0
    - 10.7.0
    - 10.8.0
    - 10.8.1
    - 10.9.0
    - 10.10.0
    - 10.10.1
    - 17.0.3
    compatible:
    - 17.0.3
    recommended: 17.0.3
    deprecated: []
  dependencies:
  - name: com.unity.shadergraph
    version: 17.0.3
  - name: com.unity.render-pipelines.core
    version: 17.0.3
  resolvedDependencies:
  - name: com.unity.shadergraph
    version: 17.0.3
  - name: com.unity.render-pipelines.core
    version: 17.0.3
  - name: com.unity.burst
    version: 1.8.18
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  - name: com.unity.searcher
    version: 4.9.2
  keywords:
  - vfx
  - visualeffect
  - graph
  - effect
  - particles
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.0.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: ba0fe1b085e90ce25110d507ce099003cddafca7
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.0.3
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.visualscripting@1.9.5
  testable: 0
  isDirectDependency: 1
  version: 1.9.5
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.visualscripting@1b53f46e931b
  assetPath: Packages/com.unity.visualscripting
  name: com.unity.visualscripting
  displayName: Visual Scripting
  author:
    name: 
    email: 
    url: 
  category: 
  type: tool
  description: 'Visual scripting is a workflow that uses visual, node-based graphs
    to design behaviors rather than write lines of C# script.


    Enabling artists,
    designers and programmers alike, visual scripting can be used to design final
    logic, quickly create prototypes, iterate on gameplay and create custom nodes
    to help streamline collaboration.


    Visual scripting is compatible with third-party
    APIs, including most packages, assets and custom libraries.'
  errors: []
  versions:
    all:
    - 1.5.0
    - 1.5.1-pre.3
    - 1.5.1-pre.5
    - 1.5.1
    - 1.5.2
    - 1.6.0-pre.3
    - 1.6.0
    - 1.6.1
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.5
    - 1.7.6
    - 1.7.7
    - 1.7.8
    - 1.8.0-pre.1
    - 1.8.0
    - 1.9.0
    - 1.9.1
    - 1.9.2
    - 1.9.4
    - 1.9.5
    - 1.9.6
    - 1.9.7
    - 1.9.8
    compatible:
    - 1.9.5
    - 1.9.6
    - 1.9.7
    - 1.9.8
    recommended: 1.9.8
    deprecated:
    - 1.8.0-pre.1
  dependencies:
  - name: com.unity.ugui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638659692270900000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.visualscripting.git
    revision: 3621ef0207747a605f195118ba99d1a8bdb19433
    path: 
  unityLifecycle:
    version: 1.9.5
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Fixed \"NullReferenceException\" error
    when returning to the State Graph from the Script Graph. [UVSB-1905](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-1905)\n-
    Fixed compilation error when a graph contains a reference to a method with an
    \"in\" parameter. [UVSB-2544](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-2487)\n-
    Added missing truncate function to Formula node [UVSB-2526](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-2525)\n-
    Fixed an error when creating a Script Graph asset in an empty project\n\n###
    Changed\n- Updated deprecated EditorAnalytics APIs to new ones"}'
  assetStore:
    productId: 
  fingerprint: 1b53f46e931bea668e53f1feb0ac9138170c9455
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.9.5
    minimumUnityVersion: 2021.3.0a1
- packageId: jillejr.newtonsoft.json-for-unity@https://github.com/jilleJr/Newtonsoft.Json-for-Unity.git#upm
  testable: 0
  isDirectDependency: 1
  version: 10.0.302
  source: 5
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\jillejr.newtonsoft.json-for-unity@f2c6cb098178
  assetPath: Packages/jillejr.newtonsoft.json-for-unity
  name: jillejr.newtonsoft.json-for-unity
  displayName: Json.NET 10.0.3 for Unity
  author:
    name: jilleJr
    email: 
    url: 
  category: Utility
  type: library
  description: "Json.NET is a popular high-performance JSON framework for .NET\n\nThis
    package is a fork of Newtonsoft.Json containing custom builds targeting standalone,
    portable (UWP, WP8), and AOT targets such as all IL2CPP builds (iOS, WebGL, Android,
    Windows, Mac OS X, et.al).\n\nThis package is licensed under The MIT License
    (MIT)\n\nCopyright \xA9 2019 Kalle Jillheden (jilleJr)\nhttps://github.com/jilleJr/Newtonsoft.Json-for-Unity\n\nCopyright
    \xA9 2016 SaladLab\nhttps://github.com/SaladLab/Json.Net.Unity3D\n\nCopyright
    \xA9 2007 James Newton-King\nhttps://github.com/JamesNK/Newtonsoft.Json\n\nSee
    full copyrights in LICENSE.md inside package"
  errors: []
  versions:
    all: []
    compatible: []
    recommended: 
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - json
  - json.net
  - newtonsoft
  - newtonsoft.json
  - unity
  - upm
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/jilleJr/Newtonsoft.Json-for-Unity.git
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: f2c6cb09817832dccd0600f291053d8d55298ee9
    revision: upm
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: f2c6cb09817832dccd0600f291053d8d55298ee9
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 
    minimumUnityVersion: 2018.1.0a1
- packageId: com.unity.modules.accessibility@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.accessibility
  assetPath: Packages/com.unity.modules.accessibility
  name: com.unity.modules.accessibility
  displayName: Accessibility
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Accessibility module includes utilities to facilitate the development
    of accessible user experiences in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AccessibilityModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.ai@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ai
  assetPath: Packages/com.unity.modules.ai
  name: com.unity.modules.ai
  displayName: AI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The AI module implements the path finding features in Unity. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.AIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.androidjni@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.androidjni
  assetPath: Packages/com.unity.modules.androidjni
  name: com.unity.modules.androidjni
  displayName: Android JNI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'AndroidJNI module allows you to call Java code. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AndroidJNIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.animation@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.animation
  assetPath: Packages/com.unity.modules.animation
  name: com.unity.modules.animation
  displayName: Animation
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Animation module implements Unity''s animation system. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.AnimationModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.assetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.assetbundle
  assetPath: Packages/com.unity.modules.assetbundle
  name: com.unity.modules.assetbundle
  displayName: Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The AssetBundle module implements the AssetBundle class and related
    APIs to load data from AssetBundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AssetBundleModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.audio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.audio
  assetPath: Packages/com.unity.modules.audio
  name: com.unity.modules.audio
  displayName: Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Audio module implements Unity''s audio system. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.AudioModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.cloth@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.cloth
  assetPath: Packages/com.unity.modules.cloth
  name: com.unity.modules.cloth
  displayName: Cloth
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Cloth module implements cloth physics simulation through the
    Cloth component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ClothModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.director@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.director
  assetPath: Packages/com.unity.modules.director
  name: com.unity.modules.director
  displayName: Director
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Director module implements the PlayableDirector class. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.DirectorModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.imageconversion@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imageconversion
  assetPath: Packages/com.unity.modules.imageconversion
  name: com.unity.modules.imageconversion
  displayName: Image Conversion
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ImageConversion module implements the ImageConversion class which
    provides helper methods for converting image data. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ImageConversionModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.imgui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imgui
  assetPath: Packages/com.unity.modules.imgui
  name: com.unity.modules.imgui
  displayName: IMGUI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The IMGUI module provides Unity''s immediate mode GUI solution for
    creating in-game and editor user interfaces. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.IMGUIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.jsonserialize@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.jsonserialize
  assetPath: Packages/com.unity.modules.jsonserialize
  name: com.unity.modules.jsonserialize
  displayName: JSONSerialize
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The JSONSerialize module provides the JsonUtility class which lets
    you serialize Unity Objects to JSON format. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.JSONSerializeModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.particlesystem@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.particlesystem
  assetPath: Packages/com.unity.modules.particlesystem
  name: com.unity.modules.particlesystem
  displayName: Particle System
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ParticleSystem module implements Unity''s Particle System. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.ParticleSystemModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.physics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics
  assetPath: Packages/com.unity.modules.physics
  name: com.unity.modules.physics
  displayName: Physics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics module implements 3D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.PhysicsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.physics2d@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics2d
  assetPath: Packages/com.unity.modules.physics2d
  name: com.unity.modules.physics2d
  displayName: Physics 2D
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics2d module implements 2D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.Physics2DModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.screencapture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.screencapture
  assetPath: Packages/com.unity.modules.screencapture
  name: com.unity.modules.screencapture
  displayName: Screen Capture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ScreenCapture module provides functionality to take screen shots
    using the ScreenCapture class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ScreenCaptureModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.terrain@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrain
  assetPath: Packages/com.unity.modules.terrain
  name: com.unity.modules.terrain
  displayName: Terrain
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Terrain module implements Unity''s Terrain rendering engine available
    through the Terrain component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.terrainphysics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrainphysics
  assetPath: Packages/com.unity.modules.terrainphysics
  name: com.unity.modules.terrainphysics
  displayName: Terrain Physics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The TerrainPhysics module connects the Terrain and Physics modules
    by implementing the TerrainCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainPhysicsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.tilemap@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.tilemap
  assetPath: Packages/com.unity.modules.tilemap
  name: com.unity.modules.tilemap
  displayName: Tilemap
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Tilemap module implements the Tilemap class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TilemapModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.ui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ui
  assetPath: Packages/com.unity.modules.ui
  name: com.unity.modules.ui
  displayName: UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UI module implements basic components required for Unity''s UI
    system Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.uielements@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.uielements
  assetPath: Packages/com.unity.modules.uielements
  name: com.unity.modules.uielements
  displayName: UIElements
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UIElements module implements the UIElements retained mode UI
    framework. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIElementsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.umbra@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.umbra
  assetPath: Packages/com.unity.modules.umbra
  name: com.unity.modules.umbra
  displayName: Umbra
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Umbra module implements Unity''s occlusion culling system. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.UmbraModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unityanalytics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unityanalytics
  assetPath: Packages/com.unity.modules.unityanalytics
  name: com.unity.modules.unityanalytics
  displayName: Unity Analytics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequest@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequest
  assetPath: Packages/com.unity.modules.unitywebrequest
  name: com.unity.modules.unitywebrequest
  displayName: Unity Web Request
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequest module lets you communicate with http services.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequestassetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestassetbundle
  assetPath: Packages/com.unity.modules.unitywebrequestassetbundle
  name: com.unity.modules.unitywebrequestassetbundle
  displayName: Unity Web Request Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAssetBundle module provides the DownloadHandlerAssetBundle
    class to use UnityWebRequest to download Asset Bundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAssetBundleModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequestaudio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestaudio
  assetPath: Packages/com.unity.modules.unitywebrequestaudio
  name: com.unity.modules.unitywebrequestaudio
  displayName: Unity Web Request Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAudio module provides the DownloadHandlerAudioClip
    class to use UnityWebRequest to download AudioClips. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAudioModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequesttexture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequesttexture
  assetPath: Packages/com.unity.modules.unitywebrequesttexture
  name: com.unity.modules.unitywebrequesttexture
  displayName: Unity Web Request Texture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestTexture module provides the DownloadHandlerTexture
    class to use UnityWebRequest to download Textures. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestTextureModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequestwww@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestwww
  assetPath: Packages/com.unity.modules.unitywebrequestwww
  name: com.unity.modules.unitywebrequestwww
  displayName: Unity Web Request WWW
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestWWW module implements the legacy WWW lets you
    communicate with http services. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestWWWModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.vehicles@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vehicles
  assetPath: Packages/com.unity.modules.vehicles
  name: com.unity.modules.vehicles
  displayName: Vehicles
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Vehicles module implements vehicle physics simulation through
    the WheelCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VehiclesModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.video@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.video
  assetPath: Packages/com.unity.modules.video
  name: com.unity.modules.video
  displayName: Video
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Video module lets you play back video files in your content.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VideoModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.vr@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vr
  assetPath: Packages/com.unity.modules.vr
  name: com.unity.modules.vr
  displayName: VR
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The VR module implements support for virtual reality devices in Unity.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VRModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.xr
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.xr
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.wind@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.wind
  assetPath: Packages/com.unity.modules.wind
  name: com.unity.modules.wind
  displayName: Wind
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Wind module implements the WindZone component which can affect
    terrain rendering and particle simulations. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.WindModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.xr@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.xr
  assetPath: Packages/com.unity.modules.xr
  name: com.unity.modules.xr
  displayName: XR
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The XR module contains the VR and AR related platform support functionality.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.XRModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.subsystems@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.subsystems
  assetPath: Packages/com.unity.modules.subsystems
  name: com.unity.modules.subsystems
  displayName: Subsystems
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Subsystem module contains the definitions and runtime support
    for general subsystems in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.SubsystemsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.hierarchycore@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.hierarchycore
  assetPath: Packages/com.unity.modules.hierarchycore
  name: com.unity.modules.hierarchycore
  displayName: Hierarchy Core
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'Module that contains a high-performance hierarchy container. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.HierarchyCoreModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.shadergraph@17.0.3
  testable: 0
  isDirectDependency: 0
  version: 17.0.3
  source: 2
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.shadergraph@911117698220
  assetPath: Packages/com.unity.shadergraph
  name: com.unity.shadergraph
  displayName: Shader Graph
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Shader Graph package adds a visual Shader editing tool to Unity.
    You can use this tool to create Shaders in a visual way instead of writing code.
    Specific render pipelines can implement specific graph features. Currently, both
    the High Definition Rendering Pipeline and the Universal Rendering Pipeline support
    Shader Graph.
  errors: []
  versions:
    all:
    - 0.1.8
    - 0.1.9
    - 0.1.17
    - 1.0.0-beta
    - 1.1.1-preview
    - 1.1.2-preview
    - 1.1.3-preview
    - 1.1.4-preview
    - 1.1.5-preview
    - 1.1.6-preview
    - 1.1.8-preview
    - 1.1.9-preview
    - 2.0.1-preview
    - 2.0.3-preview
    - 2.0.4-preview
    - 2.0.4-preview.1
    - 2.0.5-preview
    - 2.0.6-preview
    - 2.0.7-preview
    - 2.0.8-preview
    - 3.0.0-preview
    - 3.1.0-preview
    - 3.3.0-preview
    - 4.0.0-preview
    - 4.0.1-preview
    - 4.1.0-preview
    - 4.2.0-preview
    - 4.3.0-preview
    - 4.6.0-preview
    - 4.8.0-preview
    - 4.9.0-preview
    - 4.10.0-preview
    - 5.0.0-preview
    - 5.1.0
    - 5.2.0
    - 5.2.1
    - 5.2.2
    - 5.2.3
    - 5.3.1
    - 5.6.1
    - 5.7.2
    - 5.8.2
    - 5.9.0
    - 5.10.0
    - 5.13.0
    - 5.16.1
    - 6.5.2
    - 6.5.3
    - 6.7.1
    - 6.9.0
    - 6.9.1
    - 6.9.2
    - 7.0.0
    - 7.0.1
    - 7.1.1
    - 7.1.2
    - 7.1.5
    - 7.1.6
    - 7.1.7
    - 7.1.8
    - 7.2.0
    - 7.2.1
    - 7.3.1
    - 7.4.1
    - 7.4.2
    - 7.4.3
    - 7.5.1
    - 7.5.2
    - 7.5.3
    - 7.6.0
    - 7.7.0
    - 7.7.1
    - 8.0.1
    - 8.1.0
    - 8.2.0
    - 8.3.1
    - 9.0.0-preview.14
    - 9.0.0-preview.34
    - 9.0.0-preview.55
    - 9.0.0-preview.72
    - 10.0.0-preview.27
    - 10.1.0
    - 10.2.0
    - 10.2.1
    - 10.2.2
    - 10.3.1
    - 10.3.2
    - 10.4.0
    - 10.5.0
    - 10.5.1
    - 10.6.0
    - 10.7.0
    - 10.8.0
    - 10.8.1
    - 10.9.0
    - 10.10.0
    - 10.10.1
    - 17.0.3
    compatible:
    - 17.0.3
    recommended: 17.0.3
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.3
  - name: com.unity.searcher
    version: 4.9.2
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.3
  - name: com.unity.burst
    version: 1.8.18
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  - name: com.unity.searcher
    version: 4.9.2
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.0.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 9111176982208cf007caa931c3cf2716ceec4632
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.0.3
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.render-pipelines.core@17.0.3
  testable: 0
  isDirectDependency: 0
  version: 17.0.3
  source: 2
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.render-pipelines.core@7bffd5bb179a
  assetPath: Packages/com.unity.render-pipelines.core
  name: com.unity.render-pipelines.core
  displayName: Core RP Library
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: SRP Core makes it easier to create or customize a Scriptable Render
    Pipeline (SRP). SRP Core contains reusable code, including boilerplate code for
    working with platform-specific graphics APIs, utility functions for common rendering
    operations, and  shader libraries. The code in SRP Core is use by the High Definition
    Render Pipeline (HDRP) and Universal Render Pipeline (URP). If you are creating
    a custom SRP from scratch or customizing a prebuilt SRP, using SRP Core will
    save you time.
  errors: []
  versions:
    all:
    - 0.1.21
    - 0.1.27
    - 0.1.28
    - 1.0.0-beta
    - 1.0.1-beta
    - 1.1.1-preview
    - 1.1.2-preview
    - 1.1.4-preview
    - 1.1.5-preview
    - 1.1.8-preview
    - 1.1.10-preview
    - 1.1.11-preview
    - 2.0.1-preview
    - 2.0.3-preview
    - 2.0.4-preview
    - 2.0.4-preview.1
    - 2.0.5-preview
    - 2.0.6-preview
    - 2.0.7-preview
    - 2.0.8-preview
    - 3.0.0-preview
    - 3.1.0-preview
    - 3.3.0-preview
    - 4.0.0-preview
    - 4.0.1-preview
    - 4.1.0-preview
    - 4.2.0-preview
    - 4.3.0-preview
    - 4.6.0-preview
    - 4.8.0-preview
    - 4.9.0-preview
    - 4.10.0-preview
    - 5.0.0-preview
    - 5.1.0
    - 5.2.0
    - 5.2.1
    - 5.2.2
    - 5.2.3
    - 5.3.1
    - 5.6.1
    - 5.7.2
    - 5.8.2
    - 5.9.0
    - 5.10.0
    - 5.13.0
    - 5.16.1
    - 6.5.2
    - 6.5.3
    - 6.7.1
    - 6.9.0
    - 6.9.1
    - 6.9.2
    - 7.0.0
    - 7.0.1
    - 7.1.1
    - 7.1.2
    - 7.1.5
    - 7.1.6
    - 7.1.7
    - 7.1.8
    - 7.2.0
    - 7.2.1
    - 7.3.1
    - 7.4.1
    - 7.4.2
    - 7.4.3
    - 7.5.1
    - 7.5.2
    - 7.5.3
    - 7.6.0
    - 7.7.0
    - 7.7.1
    - 8.0.1
    - 8.1.0
    - 8.2.0
    - 8.3.1
    - 9.0.0-preview.13
    - 9.0.0-preview.35
    - 9.0.0-preview.38
    - 9.0.0-preview.60
    - 9.0.0-preview.77
    - 10.0.0-preview.30
    - 10.1.0
    - 10.2.0
    - 10.2.1
    - 10.2.2
    - 10.3.1
    - 10.3.2
    - 10.4.0
    - 10.5.0
    - 10.5.1
    - 10.6.0
    - 10.7.0
    - 10.8.0
    - 10.8.1
    - 10.9.0
    - 10.10.0
    - 10.10.1
    - 17.0.3
    compatible:
    - 17.0.3
    recommended: 17.0.3
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.8.14
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.collections
    version: 2.4.3
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.18
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.0.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 7bffd5bb179ae35670a1bea73d5805f4510e7add
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.0.3
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.ext.nunit@2.0.5
  testable: 0
  isDirectDependency: 0
  version: 2.0.5
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.ext.nunit@60ef35ffd3cd
  assetPath: Packages/com.unity.ext.nunit
  name: com.unity.ext.nunit
  displayName: Custom NUnit
  author:
    name: 
    email: 
    url: 
  category: Libraries
  type: assets
  description: A custom version of NUnit used by Unity Test Framework. Based on NUnit
    version 3.5 and works with all platforms, il2cpp and Mono AOT.
  errors: []
  versions:
    all:
    - 0.1.5-preview
    - 0.1.6-preview
    - 0.1.9-preview
    - 1.0.0
    - 1.0.5
    - 1.0.6
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.0.5
    compatible:
    - 2.0.5
    recommended: 2.0.5
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - nunit
  - unittest
  - test
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638236642220000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ext.nunit@2.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ext.nunit.git
    revision: 469fe0d46bfc26fbb32a53885ca4d12abcdae4ae
    path: 
  unityLifecycle:
    version: 2.0.5
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Fixing bug for InstanceID as struct changes"}'
  assetStore:
    productId: 
  fingerprint: 60ef35ffd3cd5e2f5c8887a4a4ca6148854cd092
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.5
    minimumUnityVersion: 2019.4.0a1
- packageId: com.unity.render-pipelines.universal-config@17.0.3
  testable: 0
  isDirectDependency: 0
  version: 17.0.3
  source: 2
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.render-pipelines.universal-config@fa63c96d3e1a
  assetPath: Packages/com.unity.render-pipelines.universal-config
  name: com.unity.render-pipelines.universal-config
  displayName: Universal RP Config
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Configuration files for the Universal Render Pipeline.
  errors: []
  versions:
    all:
    - 17.0.3
    compatible:
    - 17.0.3
    recommended: 17.0.3
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.3
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.3
  - name: com.unity.burst
    version: 1.8.18
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.0.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: fa63c96d3e1af317d84d171cd6c0e0658ab159df
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.0.3
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.2d.animation@10.1.4
  testable: 0
  isDirectDependency: 0
  version: 10.1.4
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.2d.animation@494a3b4e73a9
  assetPath: Packages/com.unity.2d.animation
  name: com.unity.2d.animation
  displayName: 2D Animation
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: 2D Animation provides all the necessary tooling and runtime components
    for skeletal animation using Sprites.
  errors: []
  versions:
    all:
    - 1.0.15-preview.1
    - 1.0.15-preview.2
    - 1.0.15-preview.3
    - 1.0.15-preview.4
    - 1.0.15-preview.5
    - 1.0.16-preview
    - 1.0.16-preview.1
    - 1.0.16-preview.2
    - 2.0.0-preview.1
    - 2.0.0-preview.2
    - 2.0.0-preview.3
    - 2.1.0-preview.1
    - 2.1.0-preview.2
    - 2.1.0-preview.4
    - 2.1.0-preview.5
    - 2.1.0-preview.7
    - 2.2.0-preview.1
    - 2.2.0-preview.4
    - 2.2.0-preview.5
    - 2.2.1-preview.1
    - 2.2.1-preview.2
    - 3.0.2
    - 3.0.3
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.8
    - 3.1.0
    - 3.1.1
    - 3.2.1
    - 3.2.2
    - 3.2.3
    - 3.2.4
    - 3.2.5
    - 3.2.6
    - 3.2.9
    - 3.2.10
    - 3.2.11
    - 3.2.13
    - 3.2.14
    - 3.2.15
    - 3.2.16
    - 3.2.17
    - 3.2.18
    - 4.0.0
    - 4.0.1
    - 4.1.0
    - 4.1.1
    - 4.2.1
    - 4.2.2
    - 4.2.3
    - 4.2.4
    - 4.2.5
    - 4.2.6
    - 4.2.8
    - 5.0.0
    - 5.0.1
    - 5.0.2
    - 5.0.3
    - 5.0.4
    - 5.0.5
    - 5.0.6
    - 5.0.7
    - 5.0.8
    - 5.0.9
    - 5.0.10
    - 5.1.0
    - 5.1.1
    - 5.2.0
    - 5.2.1
    - 5.2.3
    - 5.2.4
    - 5.2.6
    - 5.2.7
    - 6.0.0-pre.1
    - 6.0.0-pre.2
    - 6.0.1
    - 6.0.3
    - 6.0.4
    - 6.0.5
    - 6.0.7
    - 7.0.0-pre.2
    - 7.0.0-pre.3
    - 7.0.0
    - 7.0.1
    - 7.0.2
    - 7.0.3
    - 7.0.4
    - 7.0.5
    - 7.0.6
    - 7.0.7
    - 7.0.8
    - 7.0.9
    - 7.0.10
    - 7.0.11
    - 7.0.12
    - 7.0.13
    - 7.1.0
    - 7.1.1
    - 7.1.2
    - 7.2.0
    - 8.0.0-pre.3
    - 8.0.0-pre.4
    - 8.0.0
    - 8.0.1
    - 8.0.2
    - 8.0.3
    - 8.0.4
    - 8.0.5
    - 9.0.0-pre.1
    - 9.0.0-pre.3
    - 9.0.0
    - 9.0.1
    - 9.0.2
    - 9.0.3
    - 9.0.4
    - 9.1.0
    - 9.1.1
    - 9.1.2
    - 9.1.3
    - 9.2.0
    - 9.2.1
    - 10.0.0-pre.1
    - 10.0.0-pre.2
    - 10.0.0
    - 10.0.1
    - 10.0.2
    - 10.0.3
    - 10.1.0
    - 10.1.1
    - 10.1.2
    - 10.1.3
    - 10.1.4
    - 10.2.0
    - 10.2.1
    - 11.0.0
    - 12.0.0
    - 12.0.1
    - 12.0.2
    compatible:
    - 10.1.4
    - 10.2.0
    - 10.2.1
    recommended: 10.1.4
    deprecated: []
  dependencies:
  - name: com.unity.2d.common
    version: 9.0.7
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.collections
    version: 1.2.4
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.2d.common
    version: 9.0.7
  - name: com.unity.burst
    version: 1.8.18
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  keywords:
  - 2d
  - animation
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638654459075740000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.animation@10.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: c393ad93bdba3e78ebd30a5ccd2e6da5d8b92aba
    path: 
  unityLifecycle:
    version: 10.1.4
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Animation Preview window sometimes does
    not display deformed Sprites. (DANB-705)\n- Sprite Resolver missing sprite previews
    when dealing with large number of entries. (DANB-714)\n- Misaligned label previews
    in Sprite Resolver''s inspector. (DANB-722)\n- Sprite Resolver component not
    updated after Sprite Library Asset has been modified. (DANB-727)\n- Sprite Skin
    breaks in the animation preview window after sprite swap. (DANB-743)\n- IK gizmos
    are displayed in the SceneView when IKManager2D is active in Animation Preview
    window. (DANB-738)\n- IK solvers are misaligned when bones have different depths.
    (DANB-753)\n- Rendering issues with SRP Batching and Sprite mask. (DANB-760)\n-
    Unable to drag sprites into empty rows of the Sprite Library Editor. (DANB-749)\n-
    Sprite Skin deformation systems have outdated data after sprite swap. (DANB-766)\n-
    Setting incorrect computer buffer size. (DANB-768)\n- Reenable editor tests.\n-
    Bone buffer binding issues.\n- Sprite changed callback listeners."}'
  assetStore:
    productId: 
  fingerprint: 494a3b4e73a9ae26677ef6e9fd6bff4ca643770a
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 10.1.4
    minimumUnityVersion: 2023.1.0a1
- packageId: com.unity.2d.pixel-perfect@5.0.3
  testable: 0
  isDirectDependency: 0
  version: 5.0.3
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.2d.pixel-perfect@e3ae982b672d
  assetPath: Packages/com.unity.2d.pixel-perfect
  name: com.unity.2d.pixel-perfect
  displayName: 2D Pixel Perfect
  author:
    name: 
    email: 
    url: 
  category: 
  type: tool
  description: 'The 2D Pixel Perfect package contains the Pixel Perfect Camera component
    which ensures your pixel art remains crisp and clear at different resolutions,
    and stable in motion.


    It is a single component that makes all the calculations
    needed to scale the viewport with resolution changes, removing the hassle from
    the user. The user can adjust the definition of the pixel art rendered within
    the camera viewport through the component settings, as well preview any changes
    immediately in Game view by using the Run in Edit Mode feature.'
  errors: []
  versions:
    all:
    - 1.0.0-preview
    - 1.0.1-preview
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.1.0
    - 3.0.0
    - 3.0.1
    - 3.0.2
    - 4.0.0
    - 4.0.1
    - 5.0.0-pre.1
    - 5.0.0-pre.2
    - 5.0.0
    - 5.0.1
    - 5.0.2
    - 5.0.3
    - 5.1.0
    compatible:
    - 5.0.3
    - 5.1.0
    recommended: 5.0.3
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - pixel
  - perfect
  - 2D
  - sprite
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638023910590000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: 6dea8ac5bd6953acd64f1ac9d470912954b0c015
    path: 
  unityLifecycle:
    version: 5.0.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: e3ae982b672dc7cca42a6303bdf53b84c69991da
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 5.0.3
    minimumUnityVersion: 2021.1.0a1
- packageId: com.unity.2d.psdimporter@9.0.3
  testable: 0
  isDirectDependency: 0
  version: 9.0.3
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.2d.psdimporter@676bae148e11
  assetPath: Packages/com.unity.2d.psdimporter
  name: com.unity.2d.psdimporter
  displayName: 2D PSD Importer
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: assets
  description: A ScriptedImporter for importing Adobe Photoshop PSB (Photoshop Big)
    file format. The ScriptedImporter is currently targeted for users who wants to
    create multi Sprite character animation using Unity 2D Animation Package.
  errors: []
  versions:
    all:
    - 1.0.0-preview.1
    - 1.0.0-preview.2
    - 1.0.0-preview.3
    - 1.0.0-preview.4
    - 1.1.0-preview.1
    - 1.1.0-preview.3
    - 1.2.0-preview.1
    - 1.2.0-preview.3
    - 1.2.0-preview.4
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.0.5
    - 2.0.7
    - 2.1.0
    - 2.1.3
    - 2.1.4
    - 2.1.5
    - 2.1.6
    - 2.1.8
    - 2.1.9
    - 2.1.10
    - 2.1.11
    - 3.0.0
    - 3.1.1
    - 3.1.3
    - 3.1.4
    - 3.1.5
    - 3.1.6
    - 3.1.7
    - 4.0.0
    - 4.0.1
    - 4.0.2
    - 4.1.0
    - 4.1.1
    - 4.1.2
    - 4.1.3
    - 4.2.0
    - 4.3.0
    - 4.3.1
    - 5.0.0-pre.1
    - 5.0.0-pre.2
    - 5.0.1
    - 5.0.3
    - 5.0.4
    - 5.0.6
    - 6.0.0-pre.2
    - 6.0.0-pre.3
    - 6.0.0-pre.4
    - 6.0.0
    - 6.0.1
    - 6.0.2
    - 6.0.3
    - 6.0.4
    - 6.0.5
    - 6.0.6
    - 6.0.7
    - 6.0.8
    - 6.0.9
    - 6.1.0
    - 7.0.0-pre.3
    - 7.0.0-pre.4
    - 7.0.0
    - 7.0.1
    - 7.0.2
    - 7.0.3
    - 8.0.0-pre.1
    - 8.0.0-pre.3
    - 8.0.0
    - 8.0.1
    - 8.0.2
    - 8.0.3
    - 8.0.4
    - 8.0.5
    - 8.1.0
    - 8.1.1
    - 9.0.0-pre.1
    - 9.0.0-pre.2
    - 9.0.0
    - 9.0.1
    - 9.0.2
    - 9.0.3
    - 9.1.0
    - 10.0.0
    - 10.1.0
    - 10.1.1
    - 11.0.0
    - 11.0.1
    compatible:
    - 9.0.3
    - 9.1.0
    recommended: 9.0.3
    deprecated: []
  dependencies:
  - name: com.unity.2d.common
    version: 9.0.4
  - name: com.unity.2d.sprite
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.2d.common
    version: 9.0.7
  - name: com.unity.burst
    version: 1.8.18
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - 2d
  - psdimporter
  - assetimporter
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638489453630000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.psdimporter@9.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: cacb780673a1e5d1e9bd0e398015b1a9924c0922
    path: 
  unityLifecycle:
    version: 9.0.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Fix source file cannot be deleted after
    subsequent import. (Case DANB-579)\n\n### Changed\n- Updated the Editor Analytics
    to use the latest APIs."}'
  assetStore:
    productId: 
  fingerprint: 676bae148e11de9a02db5a3614b8c56e4f0f44ac
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 9.0.3
    minimumUnityVersion: 2023.1.0a1
- packageId: com.unity.2d.sprite@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.2d.sprite@288185b75ab4
  assetPath: Packages/com.unity.2d.sprite
  name: com.unity.2d.sprite
  displayName: 2D Sprite
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: Use Unity Sprite Editor Window to create and edit Sprite asset properties
    like pivot, borders and Physics shape
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - 2d
  - sprite
  - sprite editor window
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 288185b75ab4115633459407db6f453f23b39ae9
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 2019.2.0a1
- packageId: com.unity.2d.spriteshape@10.0.7
  testable: 0
  isDirectDependency: 0
  version: 10.0.7
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.2d.spriteshape@9e35352ae135
  assetPath: Packages/com.unity.2d.spriteshape
  name: com.unity.2d.spriteshape
  displayName: 2D SpriteShape
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: SpriteShape Runtime & Editor Package contains the tooling and the
    runtime component that allows you to create very organic looking spline based
    2D worlds. It comes with intuitive configurator and a highly performant renderer.
  errors: []
  versions:
    all:
    - 1.0.10-preview.2
    - 1.0.11-preview
    - 1.0.12-preview
    - 1.0.12-preview.1
    - 1.0.14-preview.2
    - 2.0.0-preview.4
    - 2.0.0-preview.5
    - 2.0.0-preview.7
    - 2.0.0-preview.8
    - 2.0.0-preview.9
    - 2.1.0-preview.2
    - 2.1.0-preview.6
    - 2.1.0-preview.7
    - 2.1.0-preview.10
    - 2.1.0-preview.11
    - 3.0.1
    - 3.0.2
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.7
    - 3.0.8
    - 3.0.9
    - 3.0.10
    - 3.0.11
    - 3.0.12
    - 3.0.13
    - 3.0.14
    - 3.0.15
    - 3.0.16
    - 3.0.17
    - 3.0.18
    - 4.0.0
    - 4.0.1
    - 4.0.2
    - 4.0.3
    - 4.1.0
    - 4.1.1
    - 4.1.2
    - 4.1.3
    - 4.1.4
    - 4.1.5
    - 5.0.0
    - 5.0.1
    - 5.0.2
    - 5.1.0
    - 5.1.1
    - 5.1.2
    - 5.1.3
    - 5.1.4
    - 5.1.5
    - 5.1.6
    - 5.1.7
    - 5.2.0
    - 5.3.0
    - 6.0.0-pre.1
    - 6.0.0-pre.2
    - 6.0.0
    - 6.0.1
    - 6.0.2
    - 7.0.0-pre.2
    - 7.0.0-pre.3
    - 7.0.0
    - 7.0.2
    - 7.0.3
    - 7.0.4
    - 7.0.5
    - 7.0.6
    - 7.0.7
    - 7.1.0
    - 8.0.0-pre.4
    - 8.0.0-pre.5
    - 8.0.0
    - 8.0.1
    - 9.0.0-pre.1
    - 9.0.0
    - 9.0.1
    - 9.0.2
    - 9.0.3
    - 9.0.4
    - 9.0.5
    - 9.1.0
    - 9.1.1
    - 10.0.0-pre.1
    - 10.0.0-pre.2
    - 10.0.0
    - 10.0.1
    - 10.0.2
    - 10.0.3
    - 10.0.4
    - 10.0.5
    - 10.0.6
    - 10.0.7
    - 10.1.0
    - 11.0.0
    - 12.0.0
    - 12.0.1
    compatible:
    - 10.0.7
    - 10.1.0
    recommended: 10.0.7
    deprecated: []
  dependencies:
  - name: com.unity.2d.common
    version: 9.0.7
  - name: com.unity.mathematics
    version: 1.1.0
  - name: com.unity.modules.physics2d
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.2d.common
    version: 9.0.7
  - name: com.unity.burst
    version: 1.8.18
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  - name: com.unity.modules.physics2d
    version: 1.0.0
  keywords:
  - 2d
  - shape
  - spriteshape
  - smartsprite
  - spline
  - terrain2d
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638654459104040000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.spriteshape@10.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: baf581b10d05f7aa59c58b39f4b798829f703946
    path: 
  unityLifecycle:
    version: 10.0.7
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- DANB-731 \"System.IndexOutOfRangeException\"
    is thrown when increasing Sprite Shape Mesh size beyond limits"}'
  assetStore:
    productId: 
  fingerprint: 9e35352ae135f602746220e7edc09eb95bbec530
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 10.0.7
    minimumUnityVersion: 2023.1.0a1
- packageId: com.unity.2d.tilemap@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.2d.tilemap@91020cbfae56
  assetPath: Packages/com.unity.2d.tilemap
  name: com.unity.2d.tilemap
  displayName: 2D Tilemap Editor
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: 2D Tilemap Editor is a package that contains editor functionalities
    for editing Tilemaps.
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.tilemap
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.tilemap
    version: 1.0.0
  - name: com.unity.modules.physics2d
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - 2d
  - Tilemap
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 91020cbfae5609e17fd435cd77f08d0a6b7138fb
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 2019.2.0a1
- packageId: com.unity.2d.tilemap.extras@4.1.0
  testable: 0
  isDirectDependency: 0
  version: 4.1.0
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.2d.tilemap.extras@13634da7dbe0
  assetPath: Packages/com.unity.2d.tilemap.extras
  name: com.unity.2d.tilemap.extras
  displayName: 2D Tilemap Extras
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: '2D Tilemap Extras is a package that contains extra scripts for use
    with 2D Tilemap features in Unity. These include custom Tiles and Brushes for
    the Tilemap feature.


    The following are included in the package:

    Brushes:
    GameObject Brush, Group Brush, Line Brush, Random Brush

    Tiles: Animated
    Tile, Rule Tile, Rule Override Tile

    Other: Grid Information, Custom Rules
    for Rule Tile'
  errors: []
  versions:
    all:
    - 1.5.0-preview
    - 1.6.0-preview.1
    - 1.6.1-preview
    - 1.6.2-preview
    - 1.6.3-preview
    - 1.6.4-preview
    - 1.7.0-preview
    - 1.8.0-preview
    - 1.8.1-preview
    - 1.8.2-preview
    - 1.8.3-preview
    - 1.8.4-preview
    - 2.0.0-pre.1
    - 2.0.0-pre.2
    - 2.0.0
    - 2.2.0
    - 2.2.1
    - 2.2.2
    - 2.2.3
    - 2.2.4
    - 2.2.5
    - 2.2.6
    - 2.2.7
    - 2.2.8
    - 3.0.0
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.1.0
    - 3.1.1
    - 3.1.2
    - 3.1.3
    - 4.0.0-pre.1
    - 4.0.0-pre.2
    - 4.0.0-pre.3
    - 4.0.0
    - 4.0.1
    - 4.0.2
    - 4.1.0
    - 4.2.1
    - 4.3.0
    - 4.3.1
    - 5.0.0
    - 5.0.1
    compatible:
    - 4.1.0
    recommended: 4.1.0
    deprecated: []
  dependencies:
  - name: com.unity.2d.tilemap
    version: 1.0.0
  - name: com.unity.modules.tilemap
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.2d.tilemap
    version: 1.0.0
  - name: com.unity.modules.tilemap
    version: 1.0.0
  - name: com.unity.modules.physics2d
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - 2d
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638654459126220000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.tilemap.extras@4.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/2d-extras.git
    revision: 8d9a9d9b09b21c40c727e0dfb6188875f796d9f5
    path: 
  unityLifecycle:
    version: 4.1.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n\n- [GameObjectBrush] Allow expansion of
    SceneRoot Grid foldout when clicking on label\n- [GridInformation] Fix exception
    when serializing GridInformation component if component is part of a Prefab\n-
    Remove dependency on com.unity.ugui"}'
  assetStore:
    productId: 
  fingerprint: 13634da7dbe06c39bac6bbe2d1a166cf91f58ad7
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 4.1.0
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.2d.aseprite@1.1.8
  testable: 0
  isDirectDependency: 0
  version: 1.1.8
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.2d.aseprite@1f731787b516
  assetPath: Packages/com.unity.2d.aseprite
  name: com.unity.2d.aseprite
  displayName: 2D Aseprite Importer
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: 2D Aseprite Importer is a package which enables the import of .aseprite
    files from the Pixel Art tool Aseprite.
  errors: []
  versions:
    all:
    - 1.0.0-pre.1
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0
    - 1.0.1
    - 1.1.0
    - 1.1.1
    - 1.1.2
    - 1.1.3
    - 1.1.4
    - 1.1.5
    - 1.1.6
    - 1.1.7
    - 1.1.8
    - 1.1.9
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    - 2.0.0
    - 2.0.1
    compatible:
    - 1.1.8
    - 1.1.9
    recommended: 1.1.9
    deprecated: []
  dependencies:
  - name: com.unity.2d.common
    version: 6.0.6
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.mathematics
    version: 1.2.6
  - name: com.unity.modules.animation
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.2d.common
    version: 9.0.7
  - name: com.unity.burst
    version: 1.8.18
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - 2d
  - Aseprite
  - Pixel
  - Art
  - assetimporter
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638725312306540000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.aseprite@1.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/AsepriteImporter.git
    revision: 02487451b5735520a22cdd8423415415e5cc8484
    path: 
  unityLifecycle:
    version: 1.1.8
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Fixed an issue where the importer inspector
    would throw an exception if an animation clip was empty of data. (DANB-788)\n-
    Fixed an issue where the Z-Index would not be taken into account when the import
    mode is set to \"Merge Frame\". (DANB-787)"}'
  assetStore:
    productId: 
  fingerprint: 1f731787b516be32a29864ebe53bd4e737058c54
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.1.8
    minimumUnityVersion: 2021.3.15f1
- packageId: com.unity.burst@1.8.18
  testable: 0
  isDirectDependency: 0
  version: 1.8.18
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.burst@616862665d8c
  assetPath: Packages/com.unity.burst
  name: com.unity.burst
  displayName: Burst
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Burst is a compiler that translates from IL/.NET bytecode to highly
    optimized native code using LLVM.
  errors: []
  versions:
    all:
    - 0.2.4-preview.5
    - 0.2.4-preview.7
    - 0.2.4-preview.11
    - 0.2.4-preview.12
    - 0.2.4-preview.13
    - 0.2.4-preview.14
    - 0.2.4-preview.15
    - 0.2.4-preview.16
    - 0.2.4-preview.17
    - 0.2.4-preview.18
    - 0.2.4-preview.19
    - 0.2.4-preview.20
    - 0.2.4-preview.21
    - 0.2.4-preview.22
    - 0.2.4-preview.23
    - 0.2.4-preview.24
    - 0.2.4-preview.25
    - 0.2.4-preview.30
    - 0.2.4-preview.31
    - 0.2.4-preview.33
    - 0.2.4-preview.34
    - 0.2.4-preview.37
    - 0.2.4-preview.41
    - 0.2.4-preview.45
    - 0.2.4-preview.48
    - 0.2.4-preview.50
    - 1.1.0-preview.2
    - 1.1.0-preview.3
    - 1.1.0-preview.4
    - 1.1.1
    - 1.1.2
    - 1.1.3-preview.3
    - 1.2.0-preview.1
    - 1.2.0-preview.5
    - 1.2.0-preview.6
    - 1.2.0-preview.8
    - 1.2.0-preview.9
    - 1.2.0-preview.10
    - 1.2.0-preview.11
    - 1.2.0-preview.12
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.3.0-preview.1
    - 1.3.0-preview.2
    - 1.3.0-preview.3
    - 1.3.0-preview.4
    - 1.3.0-preview.5
    - 1.3.0-preview.6
    - 1.3.0-preview.7
    - 1.3.0-preview.8
    - 1.3.0-preview.9
    - 1.3.0-preview.10
    - 1.3.0-preview.11
    - 1.3.0-preview.12
    - 1.3.0-preview.13
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.0-pre.1
    - 1.4.0-preview.1
    - 1.4.0-preview.2
    - 1.4.0-preview.3
    - 1.4.0-preview.4
    - 1.4.0-preview.5
    - 1.4.1-pre.1
    - 1.4.1-pre.2
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4-preview.1
    - 1.4.4-preview.2
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.4.7
    - 1.4.8
    - 1.4.9
    - 1.4.11
    - 1.5.0-pre.3
    - 1.5.0-pre.4
    - 1.5.0-pre.5
    - 1.5.0
    - 1.5.1
    - 1.5.2
    - 1.5.3
    - 1.5.4
    - 1.5.5
    - 1.5.6-preview.1
    - 1.5.6
    - 1.6.0-pre.2
    - 1.6.0-pre.3
    - 1.6.0-pre.4
    - 1.6.0
    - 1.6.1
    - 1.6.2
    - 1.6.3
    - 1.6.4
    - 1.6.5
    - 1.6.6
    - 1.7.0-pre.1
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.8.0-pre.1
    - 1.8.0-pre.2
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.7
    - 1.8.8
    - 1.8.9
    - 1.8.10
    - 1.8.11
    - 1.8.12
    - 1.8.13
    - 1.8.14
    - 1.8.15
    - 1.8.16
    - 1.8.17
    - 1.8.18
    - 1.8.19
    - 1.8.20
    - 1.8.21
    - 1.8.22
    - 1.8.23
    compatible:
    - 1.8.18
    - 1.8.19
    - 1.8.20
    - 1.8.21
    - 1.8.22
    - 1.8.23
    recommended: 1.8.23
    deprecated: []
  dependencies:
  - name: com.unity.mathematics
    version: 1.2.1
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638622972693870000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.burst@1.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/burst.git
    revision: 594a8d8794083be96fa510b2deea0954f971ecc9
    path: 
  unityLifecycle:
    version: 1.8.18
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n- Added the `UNITY_BURST_DISABLE_COMPILATION`
    environment variable as an alternative to the `--burst-disable-compilation` command-line
    argument\n\n### Removed\n\n### Changed\n\n### Fixed\n- Static fields used in
    static constructors were sometimes incorrectly set to read only, despite being
    written.\n- Fixed a case of the editor getting stuck loading during a domain
    reload if Burst was set to synchronous compilation\n- Fixed hashing bug that
    could occur when method signatures differed only by generic parameter count\n-
    Branches within if (cpufeaturesupported) blocks could cause the transform pass
    to miss identify which blocks are supporting which features, leading to errors
    at compile time about intrinsics not being in matching blocks.\n- Fixed ''cannot
    open input file ucrt.lib'' error when building for Universal Windows Platform
    and targeting SDK 10.0.26100.0\n\n### Known Issues"}'
  assetStore:
    productId: 
  fingerprint: 616862665d8c8ffe643d505aebb5de2ee73b0ab0
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.8.18
    minimumUnityVersion: 2020.3.0a1
- packageId: com.unity.mathematics@1.3.2
  testable: 0
  isDirectDependency: 0
  version: 1.3.2
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.mathematics@8017b507cc74
  assetPath: Packages/com.unity.mathematics
  name: com.unity.mathematics
  displayName: Mathematics
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Unity's C# SIMD math library providing vector types and math functions
    with a shader like syntax.
  errors: []
  versions:
    all:
    - 0.0.12-preview.2
    - 0.0.12-preview.5
    - 0.0.12-preview.8
    - 0.0.12-preview.10
    - 0.0.12-preview.11
    - 0.0.12-preview.13
    - 0.0.12-preview.17
    - 0.0.12-preview.19
    - 0.0.12-preview.20
    - 1.0.0-preview.1
    - 1.0.1
    - 1.1.0-preview.1
    - 1.1.0
    - 1.2.1
    - 1.2.4
    - 1.2.5
    - 1.2.6
    - 1.3.1
    - 1.3.2
    compatible:
    - 1.3.2
    recommended: 1.3.2
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638409134840000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.mathematics@1.3/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/Unity.Mathematics.git
    revision: 1695a8503482a3131be78cc26308a93f82c05b04
    path: 
  unityLifecycle:
    version: 1.3.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n* Fixed `math.hash` crash when using IL2CPP
    builds on Arm 32 bit devices.\n* Fixed obsolete method usage warnings for `MatrixDrawer.CanCacheInspectorGUI`
    and `PrimitiveVectorDrawer.CanCacheInspectorGUI` in UNITY_2023_2_OR_NEWER.\n*
    Updated minimum editor version to 2021.3"}'
  assetStore:
    productId: 
  fingerprint: 8017b507cc74bf0a1dd14b18aa860569f807314d
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.3.2
    minimumUnityVersion: 2021.3.0a1
- packageId: com.unity.collections@2.5.1
  testable: 0
  isDirectDependency: 0
  version: 2.5.1
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.collections@56bff8827a7e
  assetPath: Packages/com.unity.collections
  name: com.unity.collections
  displayName: Collections
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: A C# collections library providing data structures that can be used
    in jobs, and optimized by Burst compiler.
  errors: []
  versions:
    all:
    - 0.0.9-preview.1
    - 0.0.9-preview.2
    - 0.0.9-preview.3
    - 0.0.9-preview.4
    - 0.0.9-preview.5
    - 0.0.9-preview.6
    - 0.0.9-preview.7
    - 0.0.9-preview.8
    - 0.0.9-preview.9
    - 0.0.9-preview.10
    - 0.0.9-preview.11
    - 0.0.9-preview.12
    - 0.0.9-preview.13
    - 0.0.9-preview.14
    - 0.0.9-preview.15
    - 0.0.9-preview.16
    - 0.0.9-preview.17
    - 0.0.9-preview.18
    - 0.0.9-preview.19
    - 0.0.9-preview.20
    - 0.1.0-preview
    - 0.1.1-preview
    - 0.2.0-preview.13
    - 0.3.0-preview.0
    - 0.4.0-preview.6
    - 0.5.0-preview.9
    - 0.5.1-preview.11
    - 0.5.2-preview.8
    - 0.6.0-preview.9
    - 0.7.0-preview.2
    - 0.7.1-preview.3
    - 0.8.0-preview.5
    - 0.9.0-preview.5
    - 0.9.0-preview.6
    - 0.11.0-preview.17
    - 0.12.0-preview.13
    - 0.14.0-preview.16
    - 0.15.0-preview.21
    - 0.17.0-preview.18
    - 1.0.0-pre.3
    - 1.0.0-pre.5
    - 1.0.0-pre.6
    - 1.1.0
    - 1.2.3-pre.1
    - 1.2.3
    - 1.2.4
    - 1.3.1
    - 1.4.0
    - 1.5.1
    - 1.5.2
    - 2.1.0-exp.4
    - 2.1.0-pre.2
    - 2.1.0-pre.6
    - 2.1.0-pre.11
    - 2.1.0-pre.18
    - 2.1.1
    - 2.1.4
    - 2.2.0
    - 2.2.1
    - 2.3.0-exp.1
    - 2.3.0-pre.3
    - 2.4.0-exp.2
    - 2.4.0-pre.2
    - 2.4.0-pre.5
    - 2.4.0
    - 2.4.1
    - 2.4.2
    - 2.4.3
    - 2.5.0-exp.1
    - 2.5.0-pre.2
    - 2.5.1
    - 2.5.2
    - 2.5.3
    - 2.5.7
    - 2.6.0-exp.2
    - 2.6.0-pre.3
    compatible:
    - 2.5.1
    - 2.5.2
    - 2.5.3
    - 2.5.7
    - 2.6.0-exp.2
    - 2.6.0-pre.3
    recommended: 2.5.7
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.8.17
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.18
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  keywords:
  - dots
  - collections
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638621282722800000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.collections@2.5/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/dots.git
    revision: 5b0dea6b455f5df005c19fa984ddfa237d6cd707
    path: 
  unityLifecycle:
    version: 2.5.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changed\n* Updated Burst dependency to version
    1.8.17\n* Updated Unity Test Framework dependency to version 1.4.5\n* Updated
    entities packages dependencies\n\n### Fixed\n* Certain cases would cause an ILPostProcessor
    to fail, blocking compilation, but no more."}'
  assetStore:
    productId: 
  fingerprint: 56bff8827a7ef6d44fcee4f36e558a74da89c1a0
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.5.1
    minimumUnityVersion: 2022.3.11f1
- packageId: com.unity.rendering.light-transport@1.0.1
  testable: 0
  isDirectDependency: 0
  version: 1.0.1
  source: 2
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.rendering.light-transport@307bc27a498f
  assetPath: Packages/com.unity.rendering.light-transport
  name: com.unity.rendering.light-transport
  displayName: Unity Light Transport Library
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Unity Light Transport Library exposes reusable code for writing light
    transport algorithms such as raytracing or pathtracing
  errors: []
  versions:
    all:
    - 1.0.1
    compatible:
    - 1.0.1
    recommended: 1.0.1
    deprecated: []
  dependencies:
  - name: com.unity.collections
    version: 2.2.0
  - name: com.unity.mathematics
    version: 1.2.4
  - name: com.unity.modules.terrain
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.burst
    version: 1.8.18
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  - name: com.unity.modules.terrain
    version: 1.0.0
  keywords:
  - raytracing
  - pathtracing
  - monte-carlo
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 307bc27a498fc9cd409bbd426c85d8dc7f140bc1
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.1
    minimumUnityVersion: 2023.3.0b1
- packageId: com.unity.searcher@4.9.2
  testable: 0
  isDirectDependency: 0
  version: 4.9.2
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.searcher@90d011a70418
  assetPath: Packages/com.unity.searcher
  name: com.unity.searcher
  displayName: Searcher
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: General search window for use in the Editor. First target use is for
    GraphView node search.
  errors: []
  versions:
    all:
    - 4.0.0-preview
    - 4.0.0
    - 4.0.7-preview
    - 4.0.7
    - 4.0.8-preview
    - 4.0.9
    - 4.1.0-preview
    - 4.1.0
    - 4.2.0
    - 4.3.0
    - 4.3.1
    - 4.3.2
    - 4.6.0-preview
    - 4.7.0-preview
    - 4.9.1
    - 4.9.2
    - 4.9.3
    compatible:
    - 4.9.2
    - 4.9.3
    recommended: 4.9.3
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - search
  - searcher
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637831252240000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.searcher.git
    revision: 7f9f75b04e97cf09c8ddc3968d059e74d6a2460a
    path: 
  unityLifecycle:
    version: 4.9.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 90d011a70418fd5b0f4b8e86df39444d2af228dd
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 4.9.2
    minimumUnityVersion: 2019.1.0a1
- packageId: com.unity.2d.common@9.0.7
  testable: 0
  isDirectDependency: 0
  version: 9.0.7
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.2d.common@bb1fc9b3d81b
  assetPath: Packages/com.unity.2d.common
  name: com.unity.2d.common
  displayName: 2D Common
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: 2D Common is a package that contains shared functionalities that are
    used by most of the other 2D packages.
  errors: []
  versions:
    all:
    - 1.0.9-preview.1
    - 1.0.9-preview.2
    - 1.0.10-preview
    - 1.0.11-preview.1
    - 1.1.0-preview.1
    - 1.1.0-preview.2
    - 1.2.0-preview.1
    - 2.0.1
    - 2.0.2
    - 2.1.0
    - 2.1.1
    - 2.1.2
    - 3.0.0
    - 3.0.1
    - 4.0.0
    - 4.0.1
    - 4.0.2
    - 4.0.3
    - 4.0.4
    - 4.1.0
    - 4.2.0
    - 4.2.1
    - 5.0.0-pre.1
    - 5.0.0-pre.2
    - 5.0.0
    - 6.0.0-pre.2
    - 6.0.0-pre.3
    - 6.0.0-pre.4
    - 6.0.0
    - 6.0.1
    - 6.0.2
    - 6.0.3
    - 6.0.4
    - 6.0.5
    - 6.0.6
    - 6.0.7
    - 6.0.8
    - 6.1.0
    - 7.0.0-pre.3
    - 7.0.0-pre.4
    - 7.0.0
    - 7.0.1
    - 7.0.2
    - 7.0.3
    - 8.0.0-pre.1
    - 8.0.0-pre.2
    - 8.0.0
    - 8.0.1
    - 8.0.2
    - 8.0.3
    - 8.0.4
    - 8.1.0
    - 8.1.1
    - 9.0.0-pre.1
    - 9.0.0-pre.2
    - 9.0.0
    - 9.0.1
    - 9.0.2
    - 9.0.3
    - 9.0.4
    - 9.0.5
    - 9.0.6
    - 9.0.7
    - 9.1.0
    - 9.1.1
    - 10.0.0
    - 11.0.0
    - 11.0.1
    compatible:
    - 9.0.7
    - 9.1.0
    - 9.1.1
    recommended: 9.0.7
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.8.4
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.mathematics
    version: 1.1.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.18
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - 2d
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638654459039420000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.common@9.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: c393ad93bdba3e78ebd30a5ccd2e6da5d8b92aba
    path: 
  unityLifecycle:
    version: 9.0.7
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- DANB-638 Fixed Error \"InvalidOperationException:
    HTTP/1.1 404 Not Found\" logged when entering Play Mode in 2D Common Sample Scene\n-
    DANB-637 Fixed Sprite Atlases included in the 2D Common Package Sample \"Sprite
    Atlas Samples\" are blurry even though they are uncompressed"}'
  assetStore:
    productId: 
  fingerprint: bb1fc9b3d81b3bb452c6708e8c088fe4224a0369
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 9.0.7
    minimumUnityVersion: 2023.1.0a1
- packageId: com.unity.nuget.mono-cecil@1.11.4
  testable: 0
  isDirectDependency: 0
  version: 1.11.4
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f
  assetPath: Packages/com.unity.nuget.mono-cecil
  name: com.unity.nuget.mono-cecil
  displayName: Mono Cecil
  author:
    name: 
    email: 
    url: 
  category: 
  type: library
  description: 'The mono cecil library from https://www.nuget.org/packages/Mono.Cecil/


    This
    package is intended for internal Unity use only. Most Unity users will be better
    suite using the existing community tooling.

    To avoid assembly clashes, please
    use this package if you intend to use Mono.Cecil.'
  errors: []
  versions:
    all:
    - 0.1.6-preview.2
    - 1.0.0-preview.1
    - 1.10.0-preview.1
    - 1.10.1-preview.1
    - 1.10.1
    - 1.10.2
    - 1.11.4
    - 1.11.5
    compatible:
    - 1.11.4
    - 1.11.5
    recommended: 1.11.5
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637852971930000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.nuget.mono-cecil.git
    revision: d0133ce672d724694b56bfd20672acf6f8737fec
    path: 
  unityLifecycle:
    version: 1.11.4
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: d6f9955a5d5f84d45442ff1ad0fb694cc6e2fd62
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.11.4
    minimumUnityVersion: 2018.4.0a1
- packageId: com.unity.test-framework.performance@3.0.3
  testable: 0
  isDirectDependency: 0
  version: 3.0.3
  source: 1
  resolvedPath: D:\Menino Autista\DKG-RPG-Mobile\Library\PackageCache\com.unity.test-framework.performance@fb0dc592af8b
  assetPath: Packages/com.unity.test-framework.performance
  name: com.unity.test-framework.performance
  displayName: Performance testing API
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Package that extends Unity Test Framework package. Adds performance
    testing capabilities and collects configuration metadata.
  errors: []
  versions:
    all:
    - 0.1.27-preview
    - 0.1.29-preview
    - 0.1.31-preview
    - 0.1.33-preview
    - 0.1.34-preview
    - 0.1.36-preview
    - 0.1.37-preview
    - 0.1.39-preview
    - 0.1.40-preview
    - 0.1.41-preview
    - 0.1.42-preview
    - 0.1.44-preview
    - 0.1.45-preview
    - 0.1.47-preview
    - 0.1.48-preview
    - 0.1.49-preview
    - 0.1.50-preview
    - 1.0.4-preview
    - 1.0.6-preview
    - 1.0.9-preview
    - 1.1.2-preview
    - 1.2.0-preview
    - 1.2.1-preview
    - 1.2.3-preview
    - 1.2.5-preview
    - 1.2.6-preview
    - 1.3.0-preview
    - 1.3.1-preview
    - 1.3.2-preview
    - 1.3.3-preview
    - 2.0.1-preview
    - 2.0.2-preview
    - 2.0.3-preview
    - 2.0.6-preview
    - 2.0.7-preview
    - 2.0.8-preview
    - 2.0.9-preview
    - 2.1.0-preview
    - 2.2.0-preview
    - 2.3.1-preview
    - 2.4.1-preview
    - 2.5.1-preview
    - 2.6.0-preview
    - 2.7.0-preview
    - 2.8.0-preview
    - 2.8.1-preview
    - 3.0.0-pre.1
    - 3.0.0-pre.2
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.1.0
    compatible:
    - 3.0.3
    - 3.1.0
    recommended: 3.0.3
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.31
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - performance
  - test
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638301960470000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.test-framework.performance@3.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.test-framework.performance.git
    revision: 10b82691b2d9f4e4aa8385c8d797c7e544bac548
    path: 
  unityLifecycle:
    version: 3.0.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"## Fixed \n- Fixed issue where exception in OnTestEnded
    callback would result in EndTest method not finalising properly\n### Changed\n-
    Temporarily removed \"Open Script\" from Performance Benchmark Window\n- Some
    clarifications in documentation were added (\"Extension\" naming changed to \"Package\",
    Package limitations clarified)"}'
  assetStore:
    productId: 
  fingerprint: fb0dc592af8b524f1a06c50d7caf95552f1bcd2f
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.0.3
    minimumUnityVersion: 2020.3.0a1
m_BuiltInPackagesHash: b1ee8e5f96976b65a884948b33d89498cbe5ef4b
