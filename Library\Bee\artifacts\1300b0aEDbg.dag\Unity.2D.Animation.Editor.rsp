-target:library
-out:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll"
-refout:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.ref.dll"
-define:UNITY_6000_0_36
-define:UNITY_6000_0
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_ENGINE_CODE_STRIPPING
-define:ENABLE_ONSCREEN_KEYBOARD
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_ACCESSIBILITY
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_ANDROID
-define:UNITY_ANDROID
-define:UNITY_ANDROID_API
-define:ENABLE_EGL
-define:ENABLE_NETWORK
-define:ENABLE_RUNTIME_GI
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:UNITY_CAN_SHOW_SPLASH_SCREEN
-define:UNITY_HAS_GOOGLEVR
-define:UNITY_HAS_TANGO
-define:ENABLE_SPATIALTRACKING
-define:ENABLE_ETC_COMPRESSION
-define:PLATFORM_EXTENDS_VULKAN_DEVICE
-define:PLATFORM_HAS_MULTIPLE_SWAPCHAINS
-define:UNITY_ANDROID_SUPPORTS_SHADOWFILES
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:PLATFORM_EXTENDS_VULKAN_PIPELINE_CACHE
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:PLATFORM_HAS_ADDITIONAL_API_CHECKS
-define:ENABLE_UNITYADS_RUNTIME
-define:UNITY_UNITYADS_API
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:UNITY_POST_PROCESSING_STACK_V2
-define:DOTWEEN
-define:COLLECTIONS_2_0_OR_ABOVE
-define:USE_NEW_EDITOR_ANALYTICS
-define:ENABLE_UXML_SERIALIZED_DATA
-define:ENABLE_RUNTIME_DATA_BINDINGS
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-define:UNITY_EDITOR_ONLY_COMPILATION
-r:"Assets/Plugins/Demigiant/DOTween/DOTween.dll"
-r:"Assets/Plugins/Demigiant/DOTween/Editor/DOTweenEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.AMDModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.Apple.Extensions.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@001b54a8988a/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@001b54a8988a/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@001b54a8988a/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@001b54a8988a/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@60ef35ffd3cd/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@60ef35ffd3cd/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/PackageCache/jillejr.newtonsoft.json-for-unity@f2c6cb098178/Plugins/Newtonsoft.Json Editor/Newtonsoft.Json.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/Analytics.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/AssemblyInfo.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/ClipperLib/clipper.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/EditorUtilities.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/ICharacterDataProvider.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/LayoutOverlay/DropdownMenu.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/LayoutOverlay/LayoutOverlay.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/LayoutOverlay/LayoutOverlayUtility.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/LayoutOverlay/Manipulators/Draggable.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/LayoutOverlay/ScrollableToolbar.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/ResourceLoader.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/AssociateBonesScope.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/BaseTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/BatchedDrawing.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/BoneDrawingUtility.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Cache/BaseObject.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Cache/Cache.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Cache/CacheObject.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/CharacterModeTool/SwitchModeTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/ColorExtensions.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/CopyTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/DefaultPoseScope.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/DrawingUtility.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/EditorIconUtility.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/GenerateGeometryTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/GenerateWeightsTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/HorizontalToggleTools.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/IMGUI/Brush.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/IMGUI/GUIWrapper.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/IMGUI/ISkeletonView.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/IMGUI/ISpriteMeshView.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/IMGUI/RectSelectionTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/IMGUI/RectSlider.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/IMGUI/SkeletonController.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/IMGUI/SkeletonView.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/IMGUI/Slider2D.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/IMGUI/SpriteMeshController.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/IMGUI/SpriteMeshView.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/IMGUI/UnselectTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/IMGUI/WeightInspector.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/MathUtility.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/MeshPreviewTool/IMeshPreviewBehaviour.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/MeshPreviewTool/MeshPreviewBehaviour.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/MeshPreviewTool/MeshPreviewTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/MeshTool/MeshTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/MeshTool/MeshToolWrapper.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/ModuleToolGroup.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/ModuleUtility.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/OutlineGenerator/IOutlineGenerator.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/OutlineGenerator/OutlineGenerator.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/PivotTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Selection/BoneSelection.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Selection/IBoneSelection.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Selection/IndexedSelection.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Selection/ISelection.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Selection/ITransformSelection.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Selection/SerializableSelection.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Selection/SkeletonSelection.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SelectionTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Selectors/CircleVertexSelector.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Selectors/GenericVertexSelector.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Selectors/ICircleSelector.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Selectors/IRectSelector.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Selectors/ISelector.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Selectors/RectBoneSelector.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Selectors/RectVertexSelector.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Selectors/Unselector.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SerializableDictionary.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/ShortcutUtility.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkeletonTool/SkeletonStyles.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkeletonTool/SkeletonTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkeletonTool/SkeletonToolView.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkeletonTool/SkeletonToolWrapper.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCache/BoneCache.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCache/BoneCacheExtensions.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCache/CharacterCache.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCache/CharacterPartCache.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCache/CharacterPartCacheExtensions.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCache/MeshCache.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCache/MeshPreviewCache.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCache/SkeletonCache.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCache/SkeletonCacheExtensions.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCache/SkinningCache.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCache/SkinningCachePersistentState.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCache/SkinningEnums.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCache/SkinningEvents.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCache/SpriteCache.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCache/SpriteCacheExtensions.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCache/TransformCache.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCache/TransformCacheExtensions.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningCopyUtility.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningModule.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningModuleView.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningSerializer/ISkinningSerializer.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningSerializer/SkinningSerializerJSON.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SkinningSerializer/SkinningSerializerXML.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SpriteBoneInfluence/BoneSpriteInfluenceTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SpriteBoneInfluence/InfluenceWindow.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SpriteBoneInfluence/SpriteBoneInfluenceTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SpriteMeshData/BoneWeightExtensions.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SpriteMeshData/EditableBoneWeight.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SpriteMeshData/EditableBoneWeightUtility.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SpriteMeshData/SmoothingUtility.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SpriteMeshData/SpriteMeshData.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SpriteMeshData/SpriteMeshDataController.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/SpriteMeshData/WeightEditor.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/TextContent.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Triangulation/ITriangulator.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Triangulation/TriangulationUtility.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Triangulation/Triangulator.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/UI/BoneInspectorPanel.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/UI/BoneToolbar.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/UI/GenerateGeometryPanel.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/UI/GenerateWeightsPanel.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/UI/MeshToolbar.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/UI/PastePanel.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/UI/PoseToolbar.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/UI/RigToolbar.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/UI/Toolbar.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/UI/WeightInspectorIMGUIPanel.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/UI/WeightPainterPanel.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/UI/WeightToolbar.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Undo/DisabledUndo.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Undo/DisableUndoScope.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Undo/ICacheUndo.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Undo/IUndo.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Undo/UndoScope.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Undo/UnityEngineUndo.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/UserSettings.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/VisibilityTool/BoneReparentTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/VisibilityTool/BoneTreeViewController.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/VisibilityTool/BoneTreeViewModel.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/VisibilityTool/BoneVisibilityTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/VisibilityTool/BoneVisibilityToolInterface.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/VisibilityTool/MeshVisibilityTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/VisibilityTool/SpriteVisibilityTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/VisibilityTool/VisibilityTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/VisibilityTool/VisibilityToolBase.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/VisibilityTool/VisibilityToolColumnHeader.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/VisibilityTool/VisibilityToolResizer.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/WeightPainterTool.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/WeightPainterToolWrapper.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/WeightsGenerator/BoundedBiharmonicWeightsGenerator.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/WeightsGenerator/IWeightsGenerator.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryAssetDragAndDrop.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryAssetInspector.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryDataProvider.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryEditor/DragAndDropData.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryEditor/DragAndDropManipulator.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryEditor/SpriteLibraryAssetPostprocessor.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryEditor/SpriteLibraryEditorModel.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryEditor/SpriteLibraryEditorWindow.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryEditor/UI/CategoriesTab.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryEditor/UI/CustomElements/Dragger.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryEditor/UI/CustomElements/GridView.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryEditor/UI/CustomElements/RenamableCollections.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryEditor/UI/EditorWindowElements.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryEditor/UI/LabelsTab.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryEditor/WindowController.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryEditor/WindowEvents.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryInspector.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryPropertyString.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibrarySourceAsset/SpriteLibrarySourceAssetImporter.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibrarySourceAsset/SpriteLibrarySourceAssetImporterInspector.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibrarySourceAsset/SpriteLibrarySourceAssetPropertyString.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibrarySourceAssetFactory.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteLibraryUtilitiesEditor.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteResolverInspector.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteSelectionWidget.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteSwapOverlay/CategoryContainer.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteSwapOverlay/LabelContainer.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteSwapOverlay/OverlayToolbar.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteSwapOverlay/SpriteResolverSelector.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteSwapOverlay/SpriteSwapOverlay.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteLib/SpriteSwapOverlay/SpriteSwapVisualElement.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteOutlineRenderer/SpriteOutlineRenderer.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpritePostProcess.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteSkin/BoneEditor.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteSkin/BoneGizmo.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteSkin/BoneGizmoToggle.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteSkin/SpriteSkinEditor.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SpriteSkin/TransformExtensions.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/UpgradeTools/AnimClipUpgrader.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/UpgradeTools/AssetUpgrader.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/UpgradeTools/BaseUpgrader.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/UpgradeTools/SpriteLibUpgrader.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/UpgradeTools/UI/AssetUpgraderWindow.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/UpgradeTools/Utilities/ButtonStripField.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/UpgradeTools/Utilities/UpgradeLogWriter.cs"
"Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/UpgradeTools/Utilities/UtilityStructures.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize-
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.UnityAdditionalFile.txt"