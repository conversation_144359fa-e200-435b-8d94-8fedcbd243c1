{"name": "jillejr.newtonsoft.json-for-unity", "displayName": "Json.NET 10.0.3 for Unity", "version": "10.0.302", "unity": "2018.1", "description": "Json.NET is a popular high-performance JSON framework for .NET\n\nThis package is a fork of Newtonsoft.Json containing custom builds targeting standalone, portable (UWP, WP8), and AOT targets such as all IL2CPP builds (iOS, WebGL, Android, Windows, Mac OS X, et.al).\n\nThis package is licensed under The MIT License (MIT)\n\nCopyright © 2019 Ka<PERSON> (jilleJr)\nhttps://github.com/jilleJr/Newtonsoft.Json-for-Unity\n\nCopyright © 2016 SaladLab\nhttps://github.com/SaladLab/Json.Net.Unity3D\n\nCopyright © 2007 <PERSON>\nhttps://github.com/JamesNK/Newtonsoft.Json\n\nSee full copyrights in LICENSE.md inside package", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/jilleJr/Newtonsoft.Json-for-Unity.git", "directory": "Src/Newtonsoft.Json-for-Unity"}, "keywords": ["json", "json.net", "newtonsoft", "newtonsoft.json", "unity", "upm"], "author": "jille<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/jilleJr/Newtonsoft.Json-for-Unity/issues"}, "homepage": "https://github.com/jilleJr/Newtonsoft.Json-for-Unity#readme", "category": "Utility", "dependencies": {}, "type": "library", "_fingerprint": "f2c6cb09817832dccd0600f291053d8d55298ee9"}