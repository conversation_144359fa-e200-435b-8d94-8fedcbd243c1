{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 19104, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 19104, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 19104, "tid": 1671, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 19104, "tid": 1671, "ts": 1751301683813758, "dur": 826, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 19104, "tid": 1671, "ts": 1751301683818686, "dur": 1580, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 19104, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 19104, "tid": 1, "ts": 1751301683131079, "dur": 10452, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19104, "tid": 1, "ts": 1751301683141538, "dur": 175944, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19104, "tid": 1, "ts": 1751301683317527, "dur": 132040, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 19104, "tid": 1671, "ts": 1751301683820272, "dur": 99, "ph": "X", "name": "", "args": {}}, {"pid": 19104, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683128564, "dur": 12877, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683141444, "dur": 660790, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683142531, "dur": 3811, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683146352, "dur": 2021, "ph": "X", "name": "ProcessMessages 20487", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683148380, "dur": 587, "ph": "X", "name": "ReadAsync 20487", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683148993, "dur": 26, "ph": "X", "name": "ProcessMessages 20547", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683149021, "dur": 95, "ph": "X", "name": "ReadAsync 20547", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683149118, "dur": 9, "ph": "X", "name": "ProcessMessages 7680", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683149129, "dur": 673, "ph": "X", "name": "ReadAsync 7680", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683149809, "dur": 3, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683149814, "dur": 232, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150052, "dur": 19, "ph": "X", "name": "ProcessMessages 15079", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150083, "dur": 52, "ph": "X", "name": "ReadAsync 15079", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150137, "dur": 4, "ph": "X", "name": "ProcessMessages 3157", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150142, "dur": 156, "ph": "X", "name": "ReadAsync 3157", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150304, "dur": 3, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150308, "dur": 46, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150356, "dur": 4, "ph": "X", "name": "ProcessMessages 3053", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150361, "dur": 16, "ph": "X", "name": "ReadAsync 3053", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150379, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150381, "dur": 45, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150429, "dur": 3, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150471, "dur": 23, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150500, "dur": 3, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150505, "dur": 21, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150534, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150538, "dur": 46, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150587, "dur": 2, "ph": "X", "name": "ProcessMessages 1200", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150591, "dur": 18, "ph": "X", "name": "ReadAsync 1200", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150611, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150614, "dur": 17, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150633, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150636, "dur": 16, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150655, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150658, "dur": 15, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150674, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150711, "dur": 28, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150742, "dur": 2, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150746, "dur": 23, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150771, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150774, "dur": 93, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150870, "dur": 3, "ph": "X", "name": "ProcessMessages 1774", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150874, "dur": 22, "ph": "X", "name": "ReadAsync 1774", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150898, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150901, "dur": 18, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150922, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150924, "dur": 16, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683150995, "dur": 8, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151005, "dur": 38, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151045, "dur": 2, "ph": "X", "name": "ProcessMessages 1973", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151049, "dur": 12, "ph": "X", "name": "ReadAsync 1973", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151062, "dur": 1, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151064, "dur": 150, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151218, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151253, "dur": 2, "ph": "X", "name": "ProcessMessages 1850", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151257, "dur": 13, "ph": "X", "name": "ReadAsync 1850", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151271, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151273, "dur": 16, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151293, "dur": 38, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151339, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151342, "dur": 68, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151501, "dur": 4, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151509, "dur": 60, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151571, "dur": 5, "ph": "X", "name": "ProcessMessages 4197", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151578, "dur": 27, "ph": "X", "name": "ReadAsync 4197", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151607, "dur": 2, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151610, "dur": 16, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151629, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151632, "dur": 23, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151661, "dur": 1, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151663, "dur": 57, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151726, "dur": 3, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151731, "dur": 96, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151849, "dur": 4, "ph": "X", "name": "ProcessMessages 2628", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151855, "dur": 76, "ph": "X", "name": "ReadAsync 2628", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151933, "dur": 3, "ph": "X", "name": "ProcessMessages 2392", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151938, "dur": 17, "ph": "X", "name": "ReadAsync 2392", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151957, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151959, "dur": 16, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151978, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683151980, "dur": 52, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152035, "dur": 2, "ph": "X", "name": "ProcessMessages 1371", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152039, "dur": 18, "ph": "X", "name": "ReadAsync 1371", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152060, "dur": 1, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152062, "dur": 19, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152083, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152086, "dur": 19, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152107, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152109, "dur": 20, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152131, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152134, "dur": 11, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152158, "dur": 7, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152167, "dur": 25, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152194, "dur": 1, "ph": "X", "name": "ProcessMessages 1124", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152196, "dur": 16, "ph": "X", "name": "ReadAsync 1124", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152222, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152225, "dur": 23, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152280, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152283, "dur": 19, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152303, "dur": 8, "ph": "X", "name": "ProcessMessages 1361", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152313, "dur": 12, "ph": "X", "name": "ReadAsync 1361", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152327, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152330, "dur": 16, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152347, "dur": 40, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152389, "dur": 68, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152470, "dur": 4, "ph": "X", "name": "ProcessMessages 1431", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152476, "dur": 90, "ph": "X", "name": "ReadAsync 1431", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152569, "dur": 5, "ph": "X", "name": "ProcessMessages 3410", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152575, "dur": 61, "ph": "X", "name": "ReadAsync 3410", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152639, "dur": 2, "ph": "X", "name": "ProcessMessages 1348", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152642, "dur": 17, "ph": "X", "name": "ReadAsync 1348", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152662, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152664, "dur": 18, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152684, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152690, "dur": 52, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152748, "dur": 3, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152753, "dur": 35, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152790, "dur": 2, "ph": "X", "name": "ProcessMessages 2062", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152794, "dur": 18, "ph": "X", "name": "ReadAsync 2062", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152814, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152816, "dur": 28, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152851, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152853, "dur": 16, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152871, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152874, "dur": 72, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152952, "dur": 3, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152957, "dur": 38, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683152998, "dur": 3, "ph": "X", "name": "ProcessMessages 2312", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153003, "dur": 14, "ph": "X", "name": "ReadAsync 2312", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153018, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153021, "dur": 44, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153068, "dur": 2, "ph": "X", "name": "ProcessMessages 970", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153071, "dur": 22, "ph": "X", "name": "ReadAsync 970", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153095, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153098, "dur": 40, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153142, "dur": 2, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153145, "dur": 39, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153187, "dur": 2, "ph": "X", "name": "ProcessMessages 1555", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153190, "dur": 25, "ph": "X", "name": "ReadAsync 1555", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153220, "dur": 2, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153224, "dur": 71, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153298, "dur": 3, "ph": "X", "name": "ProcessMessages 1905", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153302, "dur": 48, "ph": "X", "name": "ReadAsync 1905", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153352, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153355, "dur": 24, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153381, "dur": 2, "ph": "X", "name": "ProcessMessages 1263", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153385, "dur": 20, "ph": "X", "name": "ReadAsync 1263", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153456, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153459, "dur": 20, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683153481, "dur": 2, "ph": "X", "name": "ProcessMessages 979", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154057, "dur": 138, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154198, "dur": 167, "ph": "X", "name": "ProcessMessages 13313", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154368, "dur": 57, "ph": "X", "name": "ReadAsync 13313", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154427, "dur": 6, "ph": "X", "name": "ProcessMessages 5049", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154436, "dur": 66, "ph": "X", "name": "ReadAsync 5049", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154505, "dur": 3, "ph": "X", "name": "ProcessMessages 2207", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154510, "dur": 15, "ph": "X", "name": "ReadAsync 2207", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154534, "dur": 2, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154538, "dur": 60, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154604, "dur": 3, "ph": "X", "name": "ProcessMessages 1842", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154608, "dur": 32, "ph": "X", "name": "ReadAsync 1842", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154643, "dur": 2, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154647, "dur": 18, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154667, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154669, "dur": 16, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154687, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154690, "dur": 57, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154750, "dur": 2, "ph": "X", "name": "ProcessMessages 1150", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154753, "dur": 18, "ph": "X", "name": "ReadAsync 1150", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154774, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154776, "dur": 99, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154878, "dur": 3, "ph": "X", "name": "ProcessMessages 2116", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154883, "dur": 18, "ph": "X", "name": "ReadAsync 2116", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154903, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154906, "dur": 27, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154936, "dur": 2, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154939, "dur": 17, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154958, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154961, "dur": 17, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154980, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683154983, "dur": 27, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155016, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155018, "dur": 97, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155117, "dur": 3, "ph": "X", "name": "ProcessMessages 2362", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155122, "dur": 81, "ph": "X", "name": "ReadAsync 2362", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155209, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155212, "dur": 33, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155249, "dur": 4, "ph": "X", "name": "ProcessMessages 1931", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155255, "dur": 23, "ph": "X", "name": "ReadAsync 1931", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155280, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155283, "dur": 68, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155352, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155355, "dur": 32, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155389, "dur": 3, "ph": "X", "name": "ProcessMessages 1910", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155393, "dur": 23, "ph": "X", "name": "ReadAsync 1910", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155419, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155421, "dur": 42, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155466, "dur": 2, "ph": "X", "name": "ProcessMessages 1154", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155470, "dur": 17, "ph": "X", "name": "ReadAsync 1154", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155488, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155491, "dur": 47, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155540, "dur": 2, "ph": "X", "name": "ProcessMessages 1217", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155544, "dur": 17, "ph": "X", "name": "ReadAsync 1217", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155562, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155565, "dur": 17, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155584, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155589, "dur": 65, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155656, "dur": 3, "ph": "X", "name": "ProcessMessages 1590", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155660, "dur": 19, "ph": "X", "name": "ReadAsync 1590", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155681, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155684, "dur": 62, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155748, "dur": 2, "ph": "X", "name": "ProcessMessages 1455", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155752, "dur": 24, "ph": "X", "name": "ReadAsync 1455", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155779, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155782, "dur": 38, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155822, "dur": 2, "ph": "X", "name": "ProcessMessages 930", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155826, "dur": 46, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155876, "dur": 2, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155880, "dur": 21, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155902, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155905, "dur": 23, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155930, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155932, "dur": 23, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155957, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683155960, "dur": 47, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156010, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156023, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156049, "dur": 2, "ph": "X", "name": "ProcessMessages 1714", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156052, "dur": 14, "ph": "X", "name": "ReadAsync 1714", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156069, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156072, "dur": 22, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156096, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156098, "dur": 24, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156124, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156126, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156150, "dur": 1, "ph": "X", "name": "ProcessMessages 1012", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156153, "dur": 21, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156176, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156179, "dur": 21, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156202, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156204, "dur": 14, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156220, "dur": 1, "ph": "X", "name": "ProcessMessages 86", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156222, "dur": 70, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156300, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156303, "dur": 432, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156739, "dur": 13, "ph": "X", "name": "ProcessMessages 1939", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156754, "dur": 116, "ph": "X", "name": "ReadAsync 1939", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156873, "dur": 13, "ph": "X", "name": "ProcessMessages 12053", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156887, "dur": 24, "ph": "X", "name": "ReadAsync 12053", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156913, "dur": 2, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156917, "dur": 39, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156958, "dur": 2, "ph": "X", "name": "ProcessMessages 1167", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156961, "dur": 16, "ph": "X", "name": "ReadAsync 1167", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156979, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683156982, "dur": 18, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157003, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157056, "dur": 30, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157088, "dur": 2, "ph": "X", "name": "ProcessMessages 1667", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157094, "dur": 14, "ph": "X", "name": "ReadAsync 1667", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157110, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157112, "dur": 33, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157148, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157150, "dur": 24, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157177, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157182, "dur": 19, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157205, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157231, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157234, "dur": 18, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157255, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157332, "dur": 22, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157356, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157359, "dur": 16, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157378, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157381, "dur": 48, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157430, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157433, "dur": 22, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157457, "dur": 2, "ph": "X", "name": "ProcessMessages 1110", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157461, "dur": 15, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157478, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157480, "dur": 49, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157533, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157557, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157560, "dur": 19, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157581, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157606, "dur": 23, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157630, "dur": 2, "ph": "X", "name": "ProcessMessages 1368", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157634, "dur": 26, "ph": "X", "name": "ReadAsync 1368", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157662, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157665, "dur": 12, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157679, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157681, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683157744, "dur": 488, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158239, "dur": 6, "ph": "X", "name": "ProcessMessages 5359", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158247, "dur": 29, "ph": "X", "name": "ReadAsync 5359", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158285, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158288, "dur": 41, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158331, "dur": 2, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158334, "dur": 29, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158367, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158415, "dur": 2, "ph": "X", "name": "ProcessMessages 937", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158418, "dur": 15, "ph": "X", "name": "ReadAsync 937", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158435, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158437, "dur": 43, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158483, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158485, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158501, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158505, "dur": 30, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158537, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158540, "dur": 54, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158598, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158638, "dur": 3, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158643, "dur": 28, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158673, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158676, "dur": 40, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158720, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683158725, "dur": 551, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159283, "dur": 8, "ph": "X", "name": "ProcessMessages 5618", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159292, "dur": 81, "ph": "X", "name": "ReadAsync 5618", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159379, "dur": 2, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159383, "dur": 22, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159465, "dur": 2, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159469, "dur": 21, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159491, "dur": 1, "ph": "X", "name": "ProcessMessages 1019", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159494, "dur": 48, "ph": "X", "name": "ReadAsync 1019", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159548, "dur": 4, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159554, "dur": 77, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159636, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159640, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159702, "dur": 3, "ph": "X", "name": "ProcessMessages 1200", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159706, "dur": 69, "ph": "X", "name": "ReadAsync 1200", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159779, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159782, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159856, "dur": 3, "ph": "X", "name": "ProcessMessages 1102", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159861, "dur": 78, "ph": "X", "name": "ReadAsync 1102", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159943, "dur": 2, "ph": "X", "name": "ProcessMessages 1098", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159947, "dur": 28, "ph": "X", "name": "ReadAsync 1098", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683159979, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160008, "dur": 20, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160030, "dur": 50, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160083, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160095, "dur": 18, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160115, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160117, "dur": 40, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160161, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160164, "dur": 59, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160226, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160228, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160250, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160253, "dur": 22, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160278, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160280, "dur": 77, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160360, "dur": 2, "ph": "X", "name": "ProcessMessages 1073", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160364, "dur": 37, "ph": "X", "name": "ReadAsync 1073", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160403, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160406, "dur": 16, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160427, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160431, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160484, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160505, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160507, "dur": 18, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160574, "dur": 2, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160578, "dur": 14, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160596, "dur": 16, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160614, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160617, "dur": 19, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160638, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160640, "dur": 16, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160658, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160660, "dur": 55, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160718, "dur": 2, "ph": "X", "name": "ProcessMessages 1033", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160721, "dur": 25, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160751, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160754, "dur": 82, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160840, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160866, "dur": 2, "ph": "X", "name": "ProcessMessages 1053", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160870, "dur": 27, "ph": "X", "name": "ReadAsync 1053", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160902, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160931, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160934, "dur": 24, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160960, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683160963, "dur": 42, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161027, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161080, "dur": 2, "ph": "X", "name": "ProcessMessages 1048", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161084, "dur": 19, "ph": "X", "name": "ReadAsync 1048", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161163, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161165, "dur": 80, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161251, "dur": 4, "ph": "X", "name": "ProcessMessages 1529", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161257, "dur": 38, "ph": "X", "name": "ReadAsync 1529", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161297, "dur": 1, "ph": "X", "name": "ProcessMessages 1112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161300, "dur": 16, "ph": "X", "name": "ReadAsync 1112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161318, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161321, "dur": 15, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161338, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161341, "dur": 19, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161375, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161378, "dur": 39, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161420, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161447, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161517, "dur": 13, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161532, "dur": 16, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161552, "dur": 23, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161577, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161579, "dur": 19, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161600, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161603, "dur": 16, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161620, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161623, "dur": 60, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161686, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161706, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161709, "dur": 48, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161759, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161773, "dur": 78, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161853, "dur": 3, "ph": "X", "name": "ProcessMessages 2256", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161858, "dur": 22, "ph": "X", "name": "ReadAsync 2256", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161888, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161891, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161912, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161915, "dur": 29, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161946, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161948, "dur": 14, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161964, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683161967, "dur": 51, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162021, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162089, "dur": 2, "ph": "X", "name": "ProcessMessages 1002", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162092, "dur": 29, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162124, "dur": 23, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162150, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162152, "dur": 44, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162198, "dur": 2, "ph": "X", "name": "ProcessMessages 1109", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162202, "dur": 58, "ph": "X", "name": "ReadAsync 1109", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162264, "dur": 19, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162286, "dur": 1, "ph": "X", "name": "ProcessMessages 88", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162288, "dur": 15, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162306, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162308, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162427, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162430, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162463, "dur": 2, "ph": "X", "name": "ProcessMessages 1156", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162468, "dur": 16, "ph": "X", "name": "ReadAsync 1156", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162490, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162573, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162576, "dur": 49, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162628, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162631, "dur": 21, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162655, "dur": 2, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162800, "dur": 37, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162839, "dur": 4, "ph": "X", "name": "ProcessMessages 3013", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162845, "dur": 26, "ph": "X", "name": "ReadAsync 3013", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162874, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162876, "dur": 44, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162924, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162952, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162955, "dur": 26, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162983, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683162986, "dur": 54, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163117, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163120, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163150, "dur": 2, "ph": "X", "name": "ProcessMessages 1068", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163154, "dur": 34, "ph": "X", "name": "ReadAsync 1068", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163195, "dur": 3, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163200, "dur": 29, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163232, "dur": 2, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163236, "dur": 47, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163320, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163324, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163397, "dur": 2, "ph": "X", "name": "ProcessMessages 1110", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163401, "dur": 31, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163433, "dur": 2, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163437, "dur": 16, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163455, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163457, "dur": 70, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163533, "dur": 3, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163538, "dur": 27, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163567, "dur": 2, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163570, "dur": 27, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163605, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163655, "dur": 2, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163659, "dur": 41, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163702, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163705, "dur": 46, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163756, "dur": 4, "ph": "X", "name": "ProcessMessages 1086", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163761, "dur": 13, "ph": "X", "name": "ReadAsync 1086", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163844, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163847, "dur": 27, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163876, "dur": 2, "ph": "X", "name": "ProcessMessages 1346", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163879, "dur": 14, "ph": "X", "name": "ReadAsync 1346", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163895, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163898, "dur": 51, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163951, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163953, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163988, "dur": 3, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683163996, "dur": 19, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164016, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164019, "dur": 53, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164136, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164140, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164200, "dur": 2, "ph": "X", "name": "ProcessMessages 1207", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164204, "dur": 74, "ph": "X", "name": "ReadAsync 1207", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164280, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164282, "dur": 24, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164338, "dur": 3, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164343, "dur": 33, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164377, "dur": 2, "ph": "X", "name": "ProcessMessages 1132", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164381, "dur": 32, "ph": "X", "name": "ReadAsync 1132", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164416, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164440, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164442, "dur": 25, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164470, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164472, "dur": 18, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164492, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164494, "dur": 44, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164541, "dur": 2, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164545, "dur": 49, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164596, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164599, "dur": 13, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164616, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164636, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164639, "dur": 18, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164659, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164662, "dur": 200, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164866, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164868, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164906, "dur": 3, "ph": "X", "name": "ProcessMessages 2457", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164918, "dur": 21, "ph": "X", "name": "ReadAsync 2457", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164940, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164943, "dur": 24, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164969, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683164972, "dur": 51, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165027, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165049, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165052, "dur": 78, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165133, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165136, "dur": 16, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165154, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165156, "dur": 56, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165214, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165217, "dur": 66, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165294, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165297, "dur": 17, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165316, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165318, "dur": 44, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165366, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165418, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165421, "dur": 17, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165439, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165442, "dur": 27, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165474, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165513, "dur": 2, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165555, "dur": 32, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165590, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165644, "dur": 29, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165675, "dur": 2, "ph": "X", "name": "ProcessMessages 1206", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165700, "dur": 39, "ph": "X", "name": "ReadAsync 1206", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165741, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165754, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165778, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165781, "dur": 19, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165802, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165805, "dur": 15, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165821, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165824, "dur": 41, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165890, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165893, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165926, "dur": 2, "ph": "X", "name": "ProcessMessages 1204", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683165930, "dur": 444, "ph": "X", "name": "ReadAsync 1204", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166381, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166471, "dur": 7, "ph": "X", "name": "ProcessMessages 5927", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166479, "dur": 23, "ph": "X", "name": "ReadAsync 5927", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166505, "dur": 2, "ph": "X", "name": "ProcessMessages 1159", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166509, "dur": 41, "ph": "X", "name": "ReadAsync 1159", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166554, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166574, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166576, "dur": 16, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166601, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166604, "dur": 21, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166627, "dur": 1, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166630, "dur": 39, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166671, "dur": 2, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166675, "dur": 68, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166746, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166749, "dur": 15, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166766, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166768, "dur": 12, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166789, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166792, "dur": 22, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166816, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166819, "dur": 85, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166909, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166933, "dur": 1, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166936, "dur": 14, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166953, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683166956, "dur": 91, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167050, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167074, "dur": 2, "ph": "X", "name": "ProcessMessages 1170", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167077, "dur": 21, "ph": "X", "name": "ReadAsync 1170", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167112, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167159, "dur": 2, "ph": "X", "name": "ProcessMessages 1171", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167162, "dur": 34, "ph": "X", "name": "ReadAsync 1171", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167200, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167223, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167226, "dur": 14, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167242, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167245, "dur": 64, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167311, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167314, "dur": 19, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167335, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167338, "dur": 15, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167355, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167357, "dur": 11, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167377, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167380, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167423, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167441, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167444, "dur": 16, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167462, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167464, "dur": 14, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167490, "dur": 3, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167494, "dur": 94, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167590, "dur": 2, "ph": "X", "name": "ProcessMessages 1182", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167594, "dur": 34, "ph": "X", "name": "ReadAsync 1182", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167632, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167676, "dur": 2, "ph": "X", "name": "ProcessMessages 1155", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167719, "dur": 112, "ph": "X", "name": "ReadAsync 1155", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167834, "dur": 3, "ph": "X", "name": "ProcessMessages 1588", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167839, "dur": 81, "ph": "X", "name": "ReadAsync 1588", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167921, "dur": 2, "ph": "X", "name": "ProcessMessages 1096", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167924, "dur": 23, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167952, "dur": 2, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683167956, "dur": 23, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168007, "dur": 2, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168010, "dur": 53, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168067, "dur": 3, "ph": "X", "name": "ProcessMessages 1770", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168072, "dur": 13, "ph": "X", "name": "ReadAsync 1770", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168086, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168088, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168143, "dur": 16, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168162, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168164, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168188, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168191, "dur": 50, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168243, "dur": 2, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168247, "dur": 15, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168264, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168267, "dur": 16, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168285, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168287, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168343, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168433, "dur": 3, "ph": "X", "name": "ProcessMessages 2244", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168486, "dur": 18, "ph": "X", "name": "ReadAsync 2244", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168506, "dur": 2, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168510, "dur": 20, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168533, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168569, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168572, "dur": 64, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168646, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168650, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683168705, "dur": 380, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169091, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169131, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169137, "dur": 27, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169170, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169176, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169217, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169225, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169263, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169269, "dur": 76, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169351, "dur": 5, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169359, "dur": 25, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169388, "dur": 4, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169395, "dur": 27, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169427, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169432, "dur": 72, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169511, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169517, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169552, "dur": 4, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169558, "dur": 32, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169595, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169602, "dur": 33, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169639, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169645, "dur": 59, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169708, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169713, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169751, "dur": 7, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169761, "dur": 35, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169802, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169807, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169844, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169851, "dur": 35, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169890, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169894, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169929, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683169936, "dur": 69, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170009, "dur": 6, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170018, "dur": 96, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170119, "dur": 10, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170131, "dur": 34, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170171, "dur": 6, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170184, "dur": 54, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170242, "dur": 6, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170250, "dur": 30, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170284, "dur": 5, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170292, "dur": 25, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170358, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170367, "dur": 37, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170408, "dur": 8, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170419, "dur": 40, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170465, "dur": 5, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170473, "dur": 73, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170552, "dur": 10, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170565, "dur": 39, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170609, "dur": 6, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170618, "dur": 30, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170653, "dur": 6, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170661, "dur": 45, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170711, "dur": 6, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170752, "dur": 39, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170796, "dur": 9, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170810, "dur": 24, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170837, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170844, "dur": 39, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170886, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683170893, "dur": 111, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171007, "dur": 6, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171016, "dur": 39, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171061, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171065, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171103, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171107, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171135, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171142, "dur": 17, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171161, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171165, "dur": 40, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171211, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171216, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171239, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171244, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171271, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171276, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171303, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171309, "dur": 48, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171361, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171367, "dur": 35, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171407, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171414, "dur": 46, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171464, "dur": 5, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171471, "dur": 18, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171491, "dur": 3, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171497, "dur": 15, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171515, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171519, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171546, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171552, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171578, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171583, "dur": 56, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171644, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171648, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171669, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171673, "dur": 240, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171918, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171922, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171974, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683171978, "dur": 9309, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683181301, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683181311, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683181351, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683181373, "dur": 489, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683181869, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683181874, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683181920, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683181925, "dur": 86, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683182017, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683182021, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683182041, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683182044, "dur": 1277, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683183329, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683183334, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683183373, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683183377, "dur": 95, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683183477, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683183481, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683183501, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683183503, "dur": 287, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683183802, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683183805, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683183834, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683183839, "dur": 163, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184006, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184009, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184030, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184034, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184070, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184076, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184099, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184102, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184142, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184145, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184164, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184167, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184217, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184220, "dur": 104, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184329, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184333, "dur": 165, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184505, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184509, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184568, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184574, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184602, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184605, "dur": 259, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184870, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184875, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184895, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184899, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184939, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184943, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184968, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184972, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683184997, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185001, "dur": 13, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185016, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185019, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185033, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185060, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185113, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185116, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185131, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185135, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185152, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185155, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185211, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185215, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185289, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185295, "dur": 85, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185384, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185388, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185427, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185430, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185448, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185452, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185479, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185505, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185520, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185523, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185590, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185595, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185637, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185641, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185661, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185664, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185693, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185697, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185720, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185723, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185738, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185742, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185807, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185811, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185828, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185831, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185943, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185947, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185966, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185969, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185982, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683185985, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186019, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186023, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186077, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186080, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186110, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186113, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186223, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186226, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186245, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186264, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186289, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186292, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186328, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186331, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186391, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186394, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186409, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186411, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186455, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186458, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186473, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186477, "dur": 11, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186490, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186493, "dur": 200, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186696, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186699, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186714, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186717, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186819, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186824, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186849, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186854, "dur": 21, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186880, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186883, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186902, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186905, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186925, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683186928, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187036, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187040, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187061, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187064, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187120, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187123, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187156, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187161, "dur": 90, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187256, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187259, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187282, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187286, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187391, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187399, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187453, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187457, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187475, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187478, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187499, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187503, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187548, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187551, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187566, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187569, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187615, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187619, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187667, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187670, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187759, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187764, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187784, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187787, "dur": 137, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187928, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187932, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187950, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683187955, "dur": 58, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188015, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188018, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188033, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188036, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188050, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188053, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188097, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188101, "dur": 65, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188172, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188176, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188212, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188216, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188233, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188236, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188317, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188323, "dur": 49, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188376, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188380, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188397, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188400, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188527, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188532, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188570, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188577, "dur": 55, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188636, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188640, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188690, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188694, "dur": 74, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188772, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188776, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188805, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188811, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188891, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188894, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188945, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683188950, "dur": 137, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683189091, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683189096, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683189122, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683189126, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683189144, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683189147, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683189252, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683189258, "dur": 50, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683189312, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683189315, "dur": 230, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683189552, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683189557, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683189593, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683189597, "dur": 707, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683190311, "dur": 1041, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191361, "dur": 64, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191432, "dur": 7, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191442, "dur": 31, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191475, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191477, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191511, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191514, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191555, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191561, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191587, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191595, "dur": 214, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191833, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191842, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191862, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191865, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191911, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191916, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191944, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191948, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191966, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683191969, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192039, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192043, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192075, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192079, "dur": 83, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192167, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192171, "dur": 102, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192279, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192284, "dur": 133, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192428, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192431, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192538, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192543, "dur": 98, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192660, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192702, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192787, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192791, "dur": 127, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192922, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683192925, "dur": 602, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683193616, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683193622, "dur": 107, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683193735, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683193739, "dur": 178, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683193942, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683193946, "dur": 202, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683194166, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683194263, "dur": 99, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683194382, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683194387, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683194429, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683194500, "dur": 290, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683194796, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683194800, "dur": 220, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683195027, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683195074, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683195159, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683195164, "dur": 719, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683195892, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683196030, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683196077, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683196083, "dur": 37577, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683234038, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683234045, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683234137, "dur": 2821, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683236969, "dur": 4984, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683241965, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683241973, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683242078, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683242087, "dur": 151, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683242242, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683242245, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683242319, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683242327, "dur": 1979, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683244334, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683244339, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683244387, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683244399, "dur": 584, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683244988, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683245003, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683245029, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683245033, "dur": 524, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683245563, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683245568, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683245592, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683245598, "dur": 1121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683246724, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683246727, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683246760, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683246779, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683246800, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683246803, "dur": 134, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683246942, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683246946, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683246980, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683246984, "dur": 191, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247180, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247184, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247235, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247239, "dur": 88, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247329, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247335, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247372, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247375, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247394, "dur": 9, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247405, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247433, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247436, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247604, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247607, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247634, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247639, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247702, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247705, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247733, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683247736, "dur": 1165, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683248905, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683248908, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683248931, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683248935, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683249024, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683249028, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683249117, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683249120, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683249136, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683249139, "dur": 418, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683249562, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683249565, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683249594, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683249597, "dur": 600, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683250226, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683250231, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683250296, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683250301, "dur": 923, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683251228, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683251231, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683251247, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683251249, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683251391, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683251394, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683251417, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683251420, "dur": 451, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683251876, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683251880, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683251900, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683251903, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683251953, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683251956, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683251983, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683251987, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683252009, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683252012, "dur": 107, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683252123, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683252126, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683252142, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683252149, "dur": 140, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683252293, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683252297, "dur": 908, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683253227, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683253230, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683253303, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683253314, "dur": 145, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683253463, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683253466, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683253491, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683253495, "dur": 175, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683253691, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683253694, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683253726, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683253730, "dur": 624, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254363, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254369, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254399, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254402, "dur": 215, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254625, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254629, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254654, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254658, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254723, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254727, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254748, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254751, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254796, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254801, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254860, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254865, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254895, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254901, "dur": 77, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254983, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683254991, "dur": 23, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255017, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255022, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255064, "dur": 5, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255071, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255102, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255108, "dur": 20, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255145, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255150, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255174, "dur": 11, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255188, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255262, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255266, "dur": 136, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255407, "dur": 5, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255414, "dur": 24, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255441, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255447, "dur": 24, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255476, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255481, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255518, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255524, "dur": 54, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255582, "dur": 5, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255620, "dur": 36, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255661, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255667, "dur": 22, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255693, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255699, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255729, "dur": 3, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255734, "dur": 20, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255841, "dur": 4, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255847, "dur": 71, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255921, "dur": 4, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683255968, "dur": 220, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683256194, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683256198, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683256775, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683256780, "dur": 86, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683256880, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683256897, "dur": 507055, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683763963, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683763969, "dur": 128, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683764103, "dur": 38, "ph": "X", "name": "ProcessMessages 1378", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683764143, "dur": 8769, "ph": "X", "name": "ReadAsync 1378", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683772927, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683772933, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683772966, "dur": 446, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 19104, "tid": 12884901888, "ts": 1751301683773416, "dur": 28709, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 19104, "tid": 1671, "ts": 1751301683820374, "dur": 5555, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 19104, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 19104, "tid": 8589934592, "ts": 1751301683123495, "dur": 326129, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 19104, "tid": 8589934592, "ts": 1751301683449627, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 19104, "tid": 8589934592, "ts": 1751301683449636, "dur": 1228, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 19104, "tid": 1671, "ts": 1751301683825934, "dur": 34, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 19104, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 19104, "tid": 4294967296, "ts": 1751301683097034, "dur": 706606, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 19104, "tid": 4294967296, "ts": 1751301683104155, "dur": 9793, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 19104, "tid": 4294967296, "ts": 1751301683803985, "dur": 6196, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 19104, "tid": 4294967296, "ts": 1751301683807850, "dur": 141, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 19104, "tid": 4294967296, "ts": 1751301683810280, "dur": 20, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 19104, "tid": 1671, "ts": 1751301683825972, "dur": 17, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751301683139007, "dur": 1674, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751301683140688, "dur": 1908, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751301683142706, "dur": 167, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751301683142874, "dur": 353, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751301683144001, "dur": 3175, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_4B40DB5AF1CE9D22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751301683147837, "dur": 2908, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751301683151151, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_2FAFF9F1339AD54A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751301683151954, "dur": 155, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751301683156201, "dur": 119, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751301683158886, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751301683160266, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18074560900205879295.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751301683161335, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751301683163469, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751301683164169, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751301683164303, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751301683143246, "dur": 27413, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751301683170675, "dur": 599084, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751301683769760, "dur": 608, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751301683774921, "dur": 60, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751301683775001, "dur": 23963, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751301683143289, "dur": 27392, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683170700, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683170777, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D07FFB3B2770754A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751301683171491, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_15728F368AD157F8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751301683172831, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751301683173094, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683173511, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2196223717492739224.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751301683173668, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683175100, "dur": 534, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751301683174016, "dur": 2131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683176147, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683176475, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683176769, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683177082, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683177357, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683177930, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683178196, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683178457, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683178785, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683179331, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683179664, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683179967, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683180250, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683180490, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683180761, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683181005, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683181272, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683181533, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683181843, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683182067, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683182356, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683182647, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683182938, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683183225, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683183591, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683184421, "dur": 1036, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683185457, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683185938, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751301683186154, "dur": 275, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751301683186430, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751301683187083, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683187397, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683187612, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751301683187775, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683187852, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751301683188374, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683188658, "dur": 793, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683189455, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751301683189691, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751301683190338, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683190428, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.SpriteShape.Runtime.ref.dll_42C04488B87F7BCB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751301683190583, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683190923, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751301683191067, "dur": 1230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751301683192363, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751301683192528, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751301683193140, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683193248, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751301683193393, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751301683193960, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751301683194079, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751301683194471, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683194560, "dur": 1966, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683196526, "dur": 45109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683241635, "dur": 2341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751301683243977, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683244041, "dur": 2398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-csharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751301683246440, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683246807, "dur": 2338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751301683249146, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683249341, "dur": 2346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751301683251691, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683251831, "dur": 2356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751301683254232, "dur": 2626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751301683256859, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683257206, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683257354, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683257734, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683257881, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751301683257978, "dur": 511805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683143458, "dur": 27294, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683170755, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7B22B0BDD3082718.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751301683171192, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_24AD401155842AFB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751301683171386, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_299E64FBD7A1C295.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751301683171512, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683172117, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751301683172825, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683172982, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683173096, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683173672, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683174034, "dur": 2137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683176171, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683176461, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683176717, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683176997, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683177331, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683177848, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683178261, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683178582, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683178887, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683179188, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683179519, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683179820, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683180082, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683180389, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683180613, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683180890, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683181171, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683181417, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683181674, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683181946, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683182428, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683182739, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683183312, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683183522, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683183902, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683183974, "dur": 1478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683185452, "dur": 717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683186169, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751301683186366, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751301683187195, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683187348, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683187810, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751301683188084, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751301683188657, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751301683189313, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683189570, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751301683189792, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751301683190413, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683190593, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683191054, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751301683191352, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751301683191897, "dur": 1439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683193337, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751301683193501, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751301683194041, "dur": 2450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683196492, "dur": 45134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683241629, "dur": 2661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751301683244291, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683244412, "dur": 2320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751301683246775, "dur": 2393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751301683249169, "dur": 2498, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683251674, "dur": 2281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751301683253956, "dur": 1277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683255239, "dur": 2657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751301683257897, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751301683257980, "dur": 511774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683143315, "dur": 27376, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683170757, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_89517EAC2A874601.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751301683171226, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_F01F6C04D397C746.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751301683171416, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_5C2FAC469D562E01.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751301683171496, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_DA3F53EC74158B55.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751301683171582, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683172016, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751301683172125, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683172242, "dur": 11682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751301683183996, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751301683184122, "dur": 1279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751301683185473, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751301683185573, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751301683185935, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751301683186074, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683186614, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751301683187338, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683187634, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751301683187768, "dur": 1116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751301683188970, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751301683189143, "dur": 1273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751301683190417, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683190624, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683191043, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751301683191251, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683191316, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751301683191859, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683191965, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751301683192152, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751301683192819, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751301683192917, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751301683193225, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683193334, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751301683193497, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751301683194079, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751301683194229, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751301683194832, "dur": 1651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683196483, "dur": 45146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683241630, "dur": 2346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751301683243977, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683244064, "dur": 2344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751301683246409, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683246795, "dur": 2393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751301683249189, "dur": 517, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683249713, "dur": 2513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751301683252226, "dur": 1274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683253506, "dur": 2186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751301683255695, "dur": 1378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683257222, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683257599, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683257883, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751301683258246, "dur": 511507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683143377, "dur": 27320, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683170699, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_7A744375D0C40DCD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751301683171223, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_1237F680F70DA85A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751301683171310, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_A1C14A714FD44069.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751301683171464, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_41BBAEC6AFBBC340.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751301683171539, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_B70C9B9527050002.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751301683172179, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_AEBFCF2A2C8F2407.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751301683172402, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751301683172555, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751301683172904, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751301683173079, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683173206, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683173292, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10054408285822421624.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751301683173649, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683173825, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683174190, "dur": 1859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683176050, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683176359, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683176735, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683177010, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683177358, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683177894, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683178183, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683178468, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683178819, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683179118, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683179421, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683179723, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683180339, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683180799, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683181105, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683181398, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683181657, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683181938, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683182299, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683182604, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683182911, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683183199, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683183474, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683183914, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683183997, "dur": 1458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683185455, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683185942, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751301683186130, "dur": 898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751301683187028, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683187263, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751301683187489, "dur": 1385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751301683188874, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683189341, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751301683189474, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751301683189881, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683190008, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Editor.ref.dll_4F4D25F4AA5D167B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751301683190368, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751301683190600, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751301683191089, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683191344, "dur": 1908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683193254, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751301683193418, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683193625, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751301683194121, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683194222, "dur": 2299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683196521, "dur": 45110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683241634, "dur": 3443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751301683245078, "dur": 1716, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683246802, "dur": 2841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751301683249644, "dur": 1369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683251020, "dur": 1965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751301683252986, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683253343, "dur": 2262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751301683255606, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751301683255772, "dur": 2533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751301683258355, "dur": 511400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683143480, "dur": 27278, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683170761, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3122CFBB2DC47329.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751301683171207, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_AB9848F6D5CE3BFE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751301683171305, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_692349F0772110A2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751301683171525, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683171621, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_099E8E7D5E4735AD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751301683172275, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751301683172764, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751301683173084, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683173269, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14507123614329245381.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751301683173630, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683174017, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17899880243675702580.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751301683174080, "dur": 1901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683175982, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683176291, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683176611, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683176995, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683177333, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683177826, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683178112, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683178388, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683178650, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683178921, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683179347, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683179619, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683179940, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683180215, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683180496, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683180756, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683180960, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683181184, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683181431, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683181731, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683181987, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683182253, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683182553, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683182844, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683183174, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683183659, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683184822, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683185473, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683185957, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751301683186170, "dur": 459, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751301683186630, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751301683187325, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751301683187463, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683187992, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751301683188227, "dur": 670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751301683188943, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751301683189651, "dur": 660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751301683190312, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683190369, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751301683190544, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683190969, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751301683191623, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683191804, "dur": 1534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683193338, "dur": 1183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683194523, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751301683194676, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751301683195043, "dur": 1475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683196519, "dur": 45117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683241639, "dur": 2342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751301683243981, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683244156, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751301683244352, "dur": 2354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751301683246759, "dur": 2245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751301683249004, "dur": 809, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683249829, "dur": 2453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751301683252282, "dur": 1698, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683253987, "dur": 2404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751301683256394, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751301683256458, "dur": 2031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751301683258541, "dur": 511219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683143531, "dur": 27238, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683170771, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AAD14E71D524EA60.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751301683171265, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_3713E40F818C5856.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751301683171421, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_76128D4AF776323F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751301683171526, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683172117, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751301683172506, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751301683173094, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683173684, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683174025, "dur": 2021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683176046, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683176346, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683176625, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683177014, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683177417, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683178003, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683178263, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683178558, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683178866, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683179176, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683179382, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683179739, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683179991, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683180267, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683180527, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683180832, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683181105, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683181377, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683181649, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683181916, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683182186, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683182454, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683182765, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683183053, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683183487, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683183767, "dur": 116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683183908, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683183975, "dur": 1475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683185450, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683185947, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751301683186126, "dur": 1062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751301683187189, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683187318, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751301683187812, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751301683188016, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751301683188649, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683188860, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751301683189631, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683189830, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683190037, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683190432, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_AC85F13A47E869BA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751301683190595, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683191098, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751301683191297, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683191353, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751301683191975, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751301683192076, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751301683192597, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683192680, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751301683192836, "dur": 1271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751301683194169, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751301683194270, "dur": 945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751301683195216, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683195290, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751301683195379, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751301683195935, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751301683196033, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751301683196373, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683196473, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751301683196693, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751301683197107, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751301683197852, "dur": 63, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751301683198215, "dur": 567788, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751301683143506, "dur": 27258, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683170766, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_98CF6329D3D6F9AB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751301683171479, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_EAC3CE9D642351D7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751301683171529, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683171598, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B72398FA1A1CE63D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751301683172056, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751301683172393, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751301683172895, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751301683173085, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683173674, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683173964, "dur": 1871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683175836, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683176492, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683176831, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683177098, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683177422, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683177935, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683178225, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683178497, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683178802, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683179103, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683179426, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683179702, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683180006, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683180308, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683180549, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683180829, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683181070, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683181364, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683181611, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683181858, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683182448, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683182746, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683183024, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683183293, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683183685, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683185061, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683185456, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683185940, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751301683186261, "dur": 1763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751301683188024, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683188140, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751301683188286, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751301683188903, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683189459, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751301683189793, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751301683190301, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683190479, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Editor.ref.dll_B651AAFD95372A05.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751301683190580, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751301683190755, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751301683191227, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683191287, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683191376, "dur": 1968, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683193344, "dur": 3179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683196523, "dur": 45116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683241641, "dur": 2348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751301683243989, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683244062, "dur": 2824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751301683246886, "dur": 773, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683247666, "dur": 2828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751301683250496, "dur": 740, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683251241, "dur": 2749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751301683253990, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683254051, "dur": 2774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751301683256826, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683256913, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683257226, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683257568, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683257724, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683257811, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751301683257894, "dur": 195043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683454797, "dur": 195, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1751301683454992, "dur": 1032, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 7, "ts": 1751301683452938, "dur": 3134, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751301683456072, "dur": 313710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683143559, "dur": 27224, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683170785, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_D5DC31B4B0DA901E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751301683171274, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_5A6241C5E4FBF31E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751301683171524, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683171655, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_5312197D285F8F1E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751301683172530, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751301683172916, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751301683173086, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683173273, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683173572, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9655524186228112463.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751301683173670, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683173999, "dur": 2113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683176112, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683176492, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683176903, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683177264, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683177842, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683178116, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683178374, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683178648, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683178873, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683179581, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683179877, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683180152, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683180463, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683180675, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683180968, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683181548, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683181787, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683182137, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683182425, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683182752, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683183022, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683183354, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683183682, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683184987, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683185458, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683186142, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751301683186335, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751301683186855, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683187050, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683187107, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751301683187544, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751301683187739, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751301683187923, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751301683188554, "dur": 343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683188922, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751301683189523, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683189600, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751301683189765, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683189828, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751301683190673, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683190948, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683191045, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751301683191263, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751301683191866, "dur": 1481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683193347, "dur": 3179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683196526, "dur": 45123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683241651, "dur": 2635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751301683244287, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683244358, "dur": 2313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751301683246672, "dur": 461, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683247138, "dur": 2314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751301683249453, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683249527, "dur": 2434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751301683251962, "dur": 2130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683254097, "dur": 2469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity-editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751301683256566, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683256739, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683256865, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683257076, "dur": 222, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/NativeFilePicker.Runtime.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751301683257521, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683257781, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683257901, "dur": 198203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751301683456104, "dur": 313679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683143543, "dur": 27234, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683170780, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_21E5C8D6C2CA360E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751301683171268, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_424FA579C047A49A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751301683171495, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_41563AEB7BF951B8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751301683173082, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683173644, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683173838, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683175023, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683176211, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683176558, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683176908, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683177200, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683177771, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683178034, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683178338, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683178616, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683178950, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683179366, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683179697, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683179979, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683180284, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683180568, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683180760, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683181024, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683181279, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683181528, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683181769, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683182043, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683182356, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683182651, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683182950, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683183214, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683183488, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683183753, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683184638, "dur": 809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683185474, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683185981, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751301683186188, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751301683186616, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683186705, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751301683186853, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683186983, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751301683187248, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751301683187997, "dur": 404, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683188407, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751301683189239, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751301683189887, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683190184, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683190397, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751301683190580, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683190746, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751301683191312, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683191651, "dur": 1698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683193349, "dur": 3176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683196525, "dur": 45132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683241658, "dur": 2310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751301683243969, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683244035, "dur": 2292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751301683246328, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683246448, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751301683248768, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683248861, "dur": 2316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751301683251233, "dur": 2494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751301683253727, "dur": 1046, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683254779, "dur": 2942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751301683257721, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683257884, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751301683258380, "dur": 511406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683143675, "dur": 27144, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683170820, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_57E453305C48C0D1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751301683171327, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_DAD3C4523F8649DE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751301683171505, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E951570DA9D21231.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751301683173090, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683173449, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/227161844219131509.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751301683173676, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683174040, "dur": 1958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683175998, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683176363, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683176701, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683176995, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683177271, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683177852, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683178147, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683178455, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683178741, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683179136, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683179426, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683179694, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683179943, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683180290, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683180572, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683180840, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683181099, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683181419, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683181695, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683182077, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683182297, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683182891, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683183169, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683183482, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683183748, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683184905, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683185480, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683185952, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751301683186233, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683186369, "dur": 849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751301683187218, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683187358, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_4A5B02DEDB940168.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751301683187697, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751301683187868, "dur": 939, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683188812, "dur": 640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751301683189452, "dur": 678, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683190140, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751301683190440, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751301683190999, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751301683191162, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751301683191676, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683191895, "dur": 1444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683193339, "dur": 2631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683195971, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751301683196073, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751301683196420, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683196521, "dur": 45141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683241664, "dur": 2373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751301683244038, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683244123, "dur": 3094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751301683247218, "dur": 2095, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683249318, "dur": 2412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751301683251730, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683251842, "dur": 2378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751301683254221, "dur": 1348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751301683255576, "dur": 2610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751301683258234, "dur": 511551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683143605, "dur": 27197, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683170805, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_BA35B04A057142E1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751301683171407, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_33DE0D3A006A6424.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751301683171521, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_8AA30A6C8DDCA8A3.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751301683172131, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751301683172298, "dur": 11011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751301683183407, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683183697, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683184882, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683185452, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683185947, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751301683186180, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751301683186987, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683187085, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751301683187298, "dur": 898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751301683188197, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683188315, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683189225, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751301683189993, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683190444, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1751301683190584, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683191202, "dur": 797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683192000, "dur": 1334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683193336, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751301683193487, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683193652, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751301683194505, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751301683194681, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751301683195086, "dur": 1431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683196518, "dur": 45115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683241638, "dur": 3651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751301683245290, "dur": 1122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683246420, "dur": 2391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751301683248812, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683248874, "dur": 2463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751301683251337, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683251676, "dur": 2384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751301683254061, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683254126, "dur": 2672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751301683257065, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751301683257176, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683257233, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683257294, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683257377, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683257886, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751301683258442, "dur": 511344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683143624, "dur": 27183, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683170809, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8AA31634E5830E2F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751301683171526, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_24D87271FC85A7AF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751301683171693, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_9B331AB6767E3EC1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751301683172286, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751301683172385, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751301683173081, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683173203, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751301683173513, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15123065923413756060.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751301683173653, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683173986, "dur": 1824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683175811, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683176914, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683177200, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683177812, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683178069, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683178397, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683178725, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683179071, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683179526, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683179878, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683180187, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683180456, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683180723, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683180987, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683181338, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683181646, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683181928, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683182160, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683182461, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683183041, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683183343, "dur": 129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683183472, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683183911, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683184004, "dur": 1449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683185453, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683186187, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751301683186373, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751301683187001, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683187092, "dur": 1877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751301683188969, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683189045, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751301683189322, "dur": 1009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751301683190332, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683190433, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Runtime.ref.dll_E68E2E6BCCC54280.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751301683190591, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683191203, "dur": 1480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683192684, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751301683192841, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751301683193341, "dur": 3135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683196477, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751301683196698, "dur": 44958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683241657, "dur": 2471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751301683244128, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683244406, "dur": 2367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751301683246774, "dur": 2049, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683248850, "dur": 2439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751301683251290, "dur": 483, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683251780, "dur": 2284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751301683254101, "dur": 3385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751301683257718, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683257892, "dur": 662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751301683258554, "dur": 511204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683143576, "dur": 27214, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683170793, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_D4E020400338F5BB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751301683171272, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_07ED47E0EFE03F3D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751301683171485, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_32AC050EF3A70AF5.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751301683172411, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751301683172982, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683173092, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683173355, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6307440961136846775.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751301683173639, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683173910, "dur": 1940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683175851, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683176087, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683176371, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683176658, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683177004, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683177364, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683177907, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683178168, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683178454, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683178858, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683179168, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683179444, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683179749, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683180016, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683180313, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683180631, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683181040, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683181319, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683181949, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683182193, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683182522, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683182825, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683183128, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683183414, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683183797, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683183907, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683183976, "dur": 1505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683185481, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683185949, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751301683186121, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751301683186606, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683186690, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683186986, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751301683187230, "dur": 930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751301683188160, "dur": 431, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683189019, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751301683189259, "dur": 902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751301683190162, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683190367, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1751301683191192, "dur": 299, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683191718, "dur": 43944, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1751301683241624, "dur": 2358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751301683243983, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683244373, "dur": 2347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751301683246772, "dur": 2345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751301683249118, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683249278, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751301683251598, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683252038, "dur": 2294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751301683254382, "dur": 2457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751301683257170, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683257267, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751301683257903, "dur": 511853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683143591, "dur": 27207, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683170800, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_E4B698B849B24AEB.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751301683171472, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_3EA836CAC6D1710A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751301683171531, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683172343, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683172429, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751301683172689, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751301683173101, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683173345, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683173651, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683173844, "dur": 1503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683175347, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683176423, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683176719, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683177054, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683177389, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683177983, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683178287, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683178575, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683178904, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683179231, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683179522, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683179764, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683180039, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683180329, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683180652, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683180965, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683181221, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683181487, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683182015, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683182306, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683182640, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683182929, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683183213, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683183973, "dur": 1481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683185454, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683185990, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751301683186173, "dur": 974, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683187150, "dur": 1624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751301683188775, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683188926, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751301683189237, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751301683189992, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683190194, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751301683190467, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751301683190948, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683191044, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751301683191208, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751301683191776, "dur": 1567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683193343, "dur": 3181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683196524, "dur": 45130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683241655, "dur": 2333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751301683243989, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683244059, "dur": 2344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751301683246404, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683246471, "dur": 2505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751301683248976, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683249054, "dur": 2478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751301683251532, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683251970, "dur": 2345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751301683254315, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683254394, "dur": 2508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751301683257131, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683257430, "dur": 502, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751301683257935, "dur": 511849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683143697, "dur": 27128, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683170826, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FB049F22F4C5497A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751301683171267, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_A5FFABD953007C60.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751301683171436, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_D3788CF92C3C660B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751301683171507, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_A5513E9150BC6315.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751301683171624, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_239A276499DBD1EB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751301683172431, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751301683172634, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1751301683173018, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751301683173119, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683173406, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683173641, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683173826, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683174134, "dur": 1861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683175995, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683176342, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683176700, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683177123, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683177406, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683177979, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683178212, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683178526, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683178951, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683179314, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683179616, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683179885, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683180177, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683180475, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683180779, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683181058, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683181390, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683181651, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683182009, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683182281, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683182567, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683182886, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683183126, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683183489, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683183899, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683183973, "dur": 1478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683185451, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683185943, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751301683186156, "dur": 467, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751301683186624, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751301683187277, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683187368, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751301683187554, "dur": 626, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683188183, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751301683188345, "dur": 2494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751301683190908, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751301683191040, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683191102, "dur": 1161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751301683192336, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751301683192431, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751301683192725, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683192803, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683193346, "dur": 3128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683196475, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751301683196701, "dur": 44943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683241646, "dur": 2373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751301683244020, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683244408, "dur": 2325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751301683246734, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683246798, "dur": 2332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751301683249131, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683249486, "dur": 2382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751301683251868, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683251989, "dur": 2388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751301683254402, "dur": 2553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751301683257028, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683257089, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751301683257264, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683257373, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683257447, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683257864, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751301683257919, "dur": 511862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683143640, "dur": 27173, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683170815, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7D19F4582D6FA60D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751301683171209, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_966AC74B818A5F90.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751301683171313, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_F48E27B3AC5122EF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751301683171516, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_EFD593047BD2E6A8.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751301683172224, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751301683172443, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683173096, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683173266, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10316626440694862930.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751301683173405, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12044039279616155683.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751301683173638, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683173749, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683174035, "dur": 1706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683175741, "dur": 1547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683177288, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683177825, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683178120, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683178392, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683178684, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683179109, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683179554, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683179830, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683180120, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683180380, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683180600, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683180955, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683181200, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683181594, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683181822, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683182078, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683182383, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683182680, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683183035, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683183346, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683183910, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683183976, "dur": 1473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683185449, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683185944, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751301683186119, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751301683186607, "dur": 494, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683187133, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751301683187430, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751301683187598, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751301683188316, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683188509, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751301683189153, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751301683189560, "dur": 890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751301683190450, "dur": 850, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683191327, "dur": 1497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683192825, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751301683192940, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751301683193237, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751301683193574, "dur": 2945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683196520, "dur": 45104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683241630, "dur": 2353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751301683243984, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683244044, "dur": 2388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751301683246486, "dur": 2190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751301683248677, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683248878, "dur": 2212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751301683251134, "dur": 2802, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751301683253989, "dur": 2410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751301683256400, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751301683256500, "dur": 1874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751301683258439, "dur": 511313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751301683802777, "dur": 1164, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 19104, "tid": 1671, "ts": 1751301683827701, "dur": 3014, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 19104, "tid": 1671, "ts": 1751301683830868, "dur": 2031, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 19104, "tid": 1671, "ts": 1751301683816706, "dur": 17298, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}