using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Test script to verify JSON save/load functionality
/// </summary>
public class JsonSaveTest : MonoBehaviour
{
    [ContextMenu("Test JSON Save/Load")]
    public void TestJsonSaveLoad()
    {
        Debug.Log("Testing JSON Save/Load System...");

        // Test volume settings
        TestVolumeSettings();
        
        // Test combo chances
        TestComboChances();
        
        // Test damage variables
        TestDamageVariables();
        
        Debug.Log("JSON Save/Load tests completed!");
    }

    private void TestVolumeSettings()
    {
        Debug.Log("Testing Volume Settings...");
        
        // Create test data
        JsonVolumeSettings testVolume = new JsonVolumeSettings
        {
            mainVolume = 0.8f,
            musicVolume = 0.6f,
            sfxVolume = 0.7f,
            musicEnabled = true,
            sfxEnabled = false
        };

        // Save to JSON
        JsonSaveHelper.SaveToJson(testVolume, "test_volume.json");
        
        // Load from JSON
        JsonVolumeSettings loadedVolume = JsonSaveHelper.LoadFromJson<JsonVolumeSettings>("test_volume.json");
        
        // Verify data
        bool success = Mathf.Approximately(testVolume.mainVolume, loadedVolume.mainVolume) &&
                      Mathf.Approximately(testVolume.musicVolume, loadedVolume.musicVolume) &&
                      Mathf.Approximately(testVolume.sfxVolume, loadedVolume.sfxVolume) &&
                      testVolume.musicEnabled == loadedVolume.musicEnabled &&
                      testVolume.sfxEnabled == loadedVolume.sfxEnabled;
        
        Debug.Log($"Volume Settings Test: {(success ? "PASSED" : "FAILED")}");
    }

    private void TestComboChances()
    {
        Debug.Log("Testing Combo Chances...");
        
        // Create test data
        JsonComboChances testCombo = new JsonComboChances
        {
            values = new int[] { 100, 90, 80, 70, 60, 50, 40 }
        };

        // Save to JSON
        JsonSaveHelper.SaveToJson(testCombo, "test_combo.json");
        
        // Load from JSON
        JsonComboChances loadedCombo = JsonSaveHelper.LoadFromJson<JsonComboChances>("test_combo.json");
        
        // Verify data
        bool success = testCombo.values.Length == loadedCombo.values.Length;
        if (success)
        {
            for (int i = 0; i < testCombo.values.Length; i++)
            {
                if (testCombo.values[i] != loadedCombo.values[i])
                {
                    success = false;
                    break;
                }
            }
        }
        
        Debug.Log($"Combo Chances Test: {(success ? "PASSED" : "FAILED")}");
    }

    private void TestDamageVariables()
    {
        Debug.Log("Testing Damage Variables...");
        
        // Create test data
        JsonDamageVariables testDamage = new JsonDamageVariables
        {
            B = 20f, C = 2.5f, D = 0.5f, E = 0.04f, F = 1f, G = 1.5f,
            Difficulty = 1f, StockWeight = 0.5f, MaxPP = 100f,
            ReductionPerCombo = 1f, FractionOfAttacksPerAction = 2f,
            ModFraction = 10, IDBOffset = 0f
        };

        // Save to JSON
        JsonSaveHelper.SaveToJson(testDamage, "test_damage.json");
        
        // Load from JSON
        JsonDamageVariables loadedDamage = JsonSaveHelper.LoadFromJson<JsonDamageVariables>("test_damage.json");
        
        // Verify data
        bool success = Mathf.Approximately(testDamage.B, loadedDamage.B) &&
                      Mathf.Approximately(testDamage.C, loadedDamage.C) &&
                      Mathf.Approximately(testDamage.D, loadedDamage.D) &&
                      Mathf.Approximately(testDamage.E, loadedDamage.E) &&
                      Mathf.Approximately(testDamage.F, loadedDamage.F) &&
                      Mathf.Approximately(testDamage.G, loadedDamage.G) &&
                      Mathf.Approximately(testDamage.Difficulty, loadedDamage.Difficulty) &&
                      Mathf.Approximately(testDamage.StockWeight, loadedDamage.StockWeight) &&
                      Mathf.Approximately(testDamage.MaxPP, loadedDamage.MaxPP) &&
                      Mathf.Approximately(testDamage.ReductionPerCombo, loadedDamage.ReductionPerCombo) &&
                      Mathf.Approximately(testDamage.FractionOfAttacksPerAction, loadedDamage.FractionOfAttacksPerAction) &&
                      testDamage.ModFraction == loadedDamage.ModFraction &&
                      Mathf.Approximately(testDamage.IDBOffset, loadedDamage.IDBOffset);
        
        Debug.Log($"Damage Variables Test: {(success ? "PASSED" : "FAILED")}");
    }

    private void TestCharacterData()
    {
        Debug.Log("Testing Character Data...");
        
        // Create test character
        BattleCharacter testChar = new BattleCharacter("1");
        testChar.name = "Test Character";
        testChar.level = 5;
        testChar.isEnemy = false;

        // Convert to JSON and back
        JsonBattleCharacter jsonChar = new JsonBattleCharacter(testChar);
        BattleCharacter loadedChar = jsonChar.ToBattleCharacter();
        
        // Verify data
        bool success = testChar.id == loadedChar.id &&
                      testChar.name == loadedChar.name &&
                      testChar.level == loadedChar.level &&
                      testChar.isEnemy == loadedChar.isEnemy;
        
        Debug.Log($"Character Data Test: {(success ? "PASSED" : "FAILED")}");
    }
}
