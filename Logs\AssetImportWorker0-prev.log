Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.36f1 (9fe3b5f71dbb) revision 10478517'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'pt' Physical Memory: 16221 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/Menino Autista/DKG-RPG-Mobile
-logFile
Logs/AssetImportWorker0.log
-srvPort
49659
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: D:/Menino Autista/DKG-RPG-Mobile
D:/Menino Autista/DKG-RPG-Mobile
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [24828]  Target information:

Player connection [24828]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 4258229367 [EditorId] 4258229367 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-J26I52U) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [24828] Host joined multi-casting on [***********:54997]...
Player connection [24828] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 242.36 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.36f1 (9fe3b5f71dbb)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Menino Autista/DKG-RPG-Mobile/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1050 Ti (ID=0x1c82)
    Vendor:   NVIDIA
    VRAM:     4004 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56780
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.004305 seconds.
- Loaded All Assemblies, in  6.293 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 314 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.743 seconds
Domain Reload Profiling: 7034ms
	BeginReloadAssembly (5456ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (222ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (554ms)
		LoadAssemblies (5455ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (551ms)
			TypeCache.Refresh (550ms)
				TypeCache.ScanAssembly (538ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (743ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (689ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (454ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (50ms)
			ProcessInitializeOnLoadAttributes (117ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  8.151 seconds
Refreshing native plugins compatible for Editor in 1.51 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.179 seconds
Domain Reload Profiling: 9327ms
	BeginReloadAssembly (160ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (7917ms)
		LoadAssemblies (7626ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (385ms)
			TypeCache.Refresh (317ms)
				TypeCache.ScanAssembly (273ms)
			BuildScriptInfoCaches (53ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1179ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (841ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (157ms)
			ProcessInitializeOnLoadAttributes (391ms)
			ProcessInitializeOnLoadMethodAttributes (268ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 1.77 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 324 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7560 unused Assets / (10.2 MB). Loaded Objects now: 8361.
Memory consumption went from 234.1 MB to 224.0 MB.
Total: 10.735300 ms (FindLiveObjects: 0.827200 ms CreateObjectMapping: 0.656000 ms MarkObjects: 5.124700 ms  DeleteObjects: 4.126200 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.684 seconds
Refreshing native plugins compatible for Editor in 0.88 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.831 seconds
Domain Reload Profiling: 1516ms
	BeginReloadAssembly (179ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (441ms)
		LoadAssemblies (349ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (175ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (154ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (832ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (638ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (165ms)
			ProcessInitializeOnLoadAttributes (382ms)
			ProcessInitializeOnLoadMethodAttributes (66ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 1.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7559 unused Assets / (10.5 MB). Loaded Objects now: 8364.
Memory consumption went from 183.8 MB to 173.3 MB.
Total: 12.545600 ms (FindLiveObjects: 0.866900 ms CreateObjectMapping: 0.916100 ms MarkObjects: 4.807500 ms  DeleteObjects: 5.954100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 346947.965903 seconds.
  path: Assets/Resources/Scripts/Configs/ImportJsonFile.cs
  artifactKey: Guid(ab6799b1f164de845bef2c0548a11f45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Scripts/Configs/ImportJsonFile.cs using Guid(ab6799b1f164de845bef2c0548a11f45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '00869f583fa0a246ff1a81c14a00dbe2') in 0.0067653 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  9.926 seconds
Refreshing native plugins compatible for Editor in 0.98 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.154 seconds
Domain Reload Profiling: 11082ms
	BeginReloadAssembly (1383ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (61ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (109ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (8478ms)
		LoadAssemblies (9324ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (287ms)
			TypeCache.Refresh (34ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (236ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1154ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (913ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (43ms)
			BeforeProcessingInitializeOnLoad (304ms)
			ProcessInitializeOnLoadAttributes (460ms)
			ProcessInitializeOnLoadMethodAttributes (88ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (35ms)
Refreshing native plugins compatible for Editor in 1.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7559 unused Assets / (10.3 MB). Loaded Objects now: 8368.
Memory consumption went from 184.4 MB to 174.1 MB.
Total: 10.682500 ms (FindLiveObjects: 0.800100 ms CreateObjectMapping: 0.637600 ms MarkObjects: 5.108800 ms  DeleteObjects: 4.134200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2212.018862 seconds.
  path: Assets/Resources/Scripts/Configs/ImportJsonFile.cs
  artifactKey: Guid(ab6799b1f164de845bef2c0548a11f45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Scripts/Configs/ImportJsonFile.cs using Guid(ab6799b1f164de845bef2c0548a11f45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8bfa85e3d8be3cd2f64b66db1a186ac1') in 0.0027105 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 2178.101838 seconds.
  path: Assets/Resources/Scripts/Configs/AddBuffButton.cs
  artifactKey: Guid(ad210c307e9c7e342a3ede20ebd41307) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Scripts/Configs/AddBuffButton.cs using Guid(ad210c307e9c7e342a3ede20ebd41307) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b1b16448f58e47c5f13973b96827ab73') in 0.0181001 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 2.538416 seconds.
  path: Assets/Resources/Scripts/Configs/LoadValues.cs
  artifactKey: Guid(19c73ab92a563d347aa1e7e10d4e2160) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Scripts/Configs/LoadValues.cs using Guid(19c73ab92a563d347aa1e7e10d4e2160) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ca6a3e8baaee485d2b1b4ffda0f69e24') in 0.0011579 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  9.525 seconds
Refreshing native plugins compatible for Editor in 1.11 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.754 seconds
Domain Reload Profiling: 10281ms
	BeginReloadAssembly (1357ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (131ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (235ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (8099ms)
		LoadAssemblies (8621ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (313ms)
			TypeCache.Refresh (184ms)
				TypeCache.ScanAssembly (157ms)
			BuildScriptInfoCaches (114ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (754ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (576ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (157ms)
			ProcessInitializeOnLoadAttributes (347ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.1 MB). Loaded Objects now: 8370.
Memory consumption went from 186.5 MB to 176.3 MB.
Total: 9.287800 ms (FindLiveObjects: 0.679100 ms CreateObjectMapping: 0.589300 ms MarkObjects: 4.033600 ms  DeleteObjects: 3.983800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1867.150804 seconds.
  path: Assets/Resources/Scripts/Configs/ComboChance.cs
  artifactKey: Guid(5f2385fac8e813f41812c203fec46264) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Scripts/Configs/ComboChance.cs using Guid(5f2385fac8e813f41812c203fec46264) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '91264eb233d0887f154993bae6b9e0dc') in 0.0095734 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.778 seconds
Refreshing native plugins compatible for Editor in 1.39 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.786 seconds
Domain Reload Profiling: 1565ms
	BeginReloadAssembly (190ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (508ms)
		LoadAssemblies (398ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (786ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (583ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (148ms)
			ProcessInitializeOnLoadAttributes (360ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.1 MB). Loaded Objects now: 8372.
Memory consumption went from 186.5 MB to 176.4 MB.
Total: 8.248700 ms (FindLiveObjects: 0.639800 ms CreateObjectMapping: 0.554600 ms MarkObjects: 3.631500 ms  DeleteObjects: 3.421400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.710 seconds
Refreshing native plugins compatible for Editor in 0.98 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.730 seconds
Domain Reload Profiling: 1442ms
	BeginReloadAssembly (174ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (478ms)
		LoadAssemblies (383ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (178ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (158ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (730ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (558ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (149ms)
			ProcessInitializeOnLoadAttributes (337ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.1 MB). Loaded Objects now: 8374.
Memory consumption went from 188.5 MB to 178.3 MB.
Total: 8.714400 ms (FindLiveObjects: 0.755800 ms CreateObjectMapping: 0.699300 ms MarkObjects: 3.725100 ms  DeleteObjects: 3.533400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.765 seconds
Refreshing native plugins compatible for Editor in 0.94 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.761 seconds
Domain Reload Profiling: 1530ms
	BeginReloadAssembly (204ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (489ms)
		LoadAssemblies (397ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (194ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (762ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (579ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (153ms)
			ProcessInitializeOnLoadAttributes (349ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.4 MB). Loaded Objects now: 8376.
Memory consumption went from 188.5 MB to 178.1 MB.
Total: 9.348900 ms (FindLiveObjects: 0.706500 ms CreateObjectMapping: 0.699500 ms MarkObjects: 3.822000 ms  DeleteObjects: 4.119800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.001 seconds
Refreshing native plugins compatible for Editor in 1.01 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.802 seconds
Domain Reload Profiling: 1805ms
	BeginReloadAssembly (305ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (56ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (88ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (631ms)
		LoadAssemblies (511ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (221ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (192ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (802ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (596ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (164ms)
			ProcessInitializeOnLoadAttributes (353ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.2 MB). Loaded Objects now: 8378.
Memory consumption went from 188.4 MB to 178.3 MB.
Total: 9.160000 ms (FindLiveObjects: 0.659200 ms CreateObjectMapping: 0.654800 ms MarkObjects: 3.983300 ms  DeleteObjects: 3.861000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 926.701497 seconds.
  path: Assets/Resources/Scripts/Characters/CharacterMods.cs
  artifactKey: Guid(c458b074a0ee0fb40be79a55bbcf36ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Scripts/Characters/CharacterMods.cs using Guid(c458b074a0ee0fb40be79a55bbcf36ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '58a10fcd707cd0ccfc3821532eadf597') in 0.0321291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  7.980 seconds
Refreshing native plugins compatible for Editor in 1.04 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.549 seconds
Domain Reload Profiling: 9532ms
	BeginReloadAssembly (1427ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (66ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (148ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (6486ms)
		LoadAssemblies (7373ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (232ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (197ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1550ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1290ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (46ms)
			BeforeProcessingInitializeOnLoad (236ms)
			ProcessInitializeOnLoadAttributes (924ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (9.8 MB). Loaded Objects now: 8380.
Memory consumption went from 188.4 MB to 178.5 MB.
Total: 9.163200 ms (FindLiveObjects: 0.668500 ms CreateObjectMapping: 0.591700 ms MarkObjects: 4.055500 ms  DeleteObjects: 3.841400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7549 unused Assets / (9.0 MB). Loaded Objects now: 8380.
Memory consumption went from 188.6 MB to 179.6 MB.
Total: 13.572900 ms (FindLiveObjects: 0.861400 ms CreateObjectMapping: 0.684800 ms MarkObjects: 8.584600 ms  DeleteObjects: 3.438900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.880 seconds
Refreshing native plugins compatible for Editor in 1.15 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.834 seconds
Domain Reload Profiling: 1716ms
	BeginReloadAssembly (219ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (593ms)
		LoadAssemblies (478ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (205ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (179ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (834ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (629ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (174ms)
			ProcessInitializeOnLoadAttributes (381ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.2 MB). Loaded Objects now: 8382.
Memory consumption went from 188.4 MB to 178.2 MB.
Total: 9.275600 ms (FindLiveObjects: 0.714500 ms CreateObjectMapping: 0.608200 ms MarkObjects: 4.120900 ms  DeleteObjects: 3.830700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2792.099383 seconds.
  path: Assets/Resources/Scripts/Configs/ImportJsonFile.cs
  artifactKey: Guid(ab6799b1f164de845bef2c0548a11f45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Scripts/Configs/ImportJsonFile.cs using Guid(ab6799b1f164de845bef2c0548a11f45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3aa8c6e321531defa08404072cc8fb82') in 0.0835521 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7549 unused Assets / (9.6 MB). Loaded Objects now: 8382.
Memory consumption went from 188.6 MB to 179.0 MB.
Total: 53.855800 ms (FindLiveObjects: 1.022200 ms CreateObjectMapping: 0.688200 ms MarkObjects: 42.266000 ms  DeleteObjects: 9.876800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  5.712 seconds
Refreshing native plugins compatible for Editor in 0.95 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.791 seconds
Domain Reload Profiling: 6505ms
	BeginReloadAssembly (1224ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (43ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (110ms)
	RebuildCommonClasses (49ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (4399ms)
		LoadAssemblies (5149ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (222ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (192ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (792ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (600ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (166ms)
			ProcessInitializeOnLoadAttributes (360ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.0 MB). Loaded Objects now: 8384.
Memory consumption went from 188.4 MB to 178.4 MB.
Total: 10.116800 ms (FindLiveObjects: 0.688300 ms CreateObjectMapping: 0.656300 ms MarkObjects: 4.525300 ms  DeleteObjects: 4.245700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.773 seconds
Refreshing native plugins compatible for Editor in 0.87 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.804 seconds
Domain Reload Profiling: 1578ms
	BeginReloadAssembly (176ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (533ms)
		LoadAssemblies (429ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (187ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (805ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (620ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (167ms)
			ProcessInitializeOnLoadAttributes (375ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.0 MB). Loaded Objects now: 8386.
Memory consumption went from 188.5 MB to 178.5 MB.
Total: 8.236700 ms (FindLiveObjects: 0.665000 ms CreateObjectMapping: 0.553400 ms MarkObjects: 3.559900 ms  DeleteObjects: 3.457600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.70 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7549 unused Assets / (9.9 MB). Loaded Objects now: 8386.
Memory consumption went from 188.6 MB to 178.7 MB.
Total: 41.098500 ms (FindLiveObjects: 0.990400 ms CreateObjectMapping: 0.682700 ms MarkObjects: 35.252400 ms  DeleteObjects: 4.170100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.200 seconds
Refreshing native plugins compatible for Editor in 0.95 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.750 seconds
Domain Reload Profiling: 2953ms
	BeginReloadAssembly (244ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (1888ms)
		LoadAssemblies (1766ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (214ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (185ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (751ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (570ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (152ms)
			ProcessInitializeOnLoadAttributes (341ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.0 MB). Loaded Objects now: 8388.
Memory consumption went from 188.4 MB to 178.4 MB.
Total: 8.432200 ms (FindLiveObjects: 0.627300 ms CreateObjectMapping: 0.614400 ms MarkObjects: 3.736700 ms  DeleteObjects: 3.452600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.784 seconds
Refreshing native plugins compatible for Editor in 0.89 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.767 seconds
Domain Reload Profiling: 1553ms
	BeginReloadAssembly (178ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (538ms)
		LoadAssemblies (424ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (196ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (768ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (590ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (162ms)
			ProcessInitializeOnLoadAttributes (353ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.0 MB). Loaded Objects now: 8390.
Memory consumption went from 188.5 MB to 178.4 MB.
Total: 8.395500 ms (FindLiveObjects: 0.638100 ms CreateObjectMapping: 0.548700 ms MarkObjects: 3.577000 ms  DeleteObjects: 3.631000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  7.033 seconds
Refreshing native plugins compatible for Editor in 1.06 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.913 seconds
Domain Reload Profiling: 7950ms
	BeginReloadAssembly (1284ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (74ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (153ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (5686ms)
		LoadAssemblies (6382ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (245ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (217ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (914ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (694ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (177ms)
			ProcessInitializeOnLoadAttributes (430ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 1.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.3 MB). Loaded Objects now: 8392.
Memory consumption went from 188.5 MB to 178.2 MB.
Total: 9.348400 ms (FindLiveObjects: 0.676300 ms CreateObjectMapping: 0.598600 ms MarkObjects: 3.998700 ms  DeleteObjects: 4.073500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  4.156 seconds
Refreshing native plugins compatible for Editor in 1.24 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.827 seconds
Domain Reload Profiling: 4985ms
	BeginReloadAssembly (407ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (54ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (106ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (3663ms)
		LoadAssemblies (3611ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (227ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (194ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (827ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (622ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (157ms)
			ProcessInitializeOnLoadAttributes (381ms)
			ProcessInitializeOnLoadMethodAttributes (61ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.2 MB). Loaded Objects now: 8394.
Memory consumption went from 188.5 MB to 178.2 MB.
Total: 9.283500 ms (FindLiveObjects: 0.701100 ms CreateObjectMapping: 0.534100 ms MarkObjects: 4.093100 ms  DeleteObjects: 3.953400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.813 seconds
Refreshing native plugins compatible for Editor in 0.90 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.780 seconds
Domain Reload Profiling: 1596ms
	BeginReloadAssembly (172ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (584ms)
		LoadAssemblies (474ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (186ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (780ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (597ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (152ms)
			ProcessInitializeOnLoadAttributes (364ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.1 MB). Loaded Objects now: 8396.
Memory consumption went from 188.5 MB to 178.4 MB.
Total: 8.355000 ms (FindLiveObjects: 0.672400 ms CreateObjectMapping: 0.549700 ms MarkObjects: 3.594800 ms  DeleteObjects: 3.537500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7549 unused Assets / (9.4 MB). Loaded Objects now: 8396.
Memory consumption went from 188.6 MB to 179.2 MB.
Total: 33.548500 ms (FindLiveObjects: 1.013700 ms CreateObjectMapping: 0.911100 ms MarkObjects: 8.786300 ms  DeleteObjects: 22.834200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.463 seconds
Refreshing native plugins compatible for Editor in 1.07 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.818 seconds
Domain Reload Profiling: 2282ms
	BeginReloadAssembly (218ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (1182ms)
		LoadAssemblies (1067ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (203ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (174ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (818ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (624ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (164ms)
			ProcessInitializeOnLoadAttributes (386ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.2 MB). Loaded Objects now: 8398.
Memory consumption went from 188.5 MB to 178.3 MB.
Total: 8.535100 ms (FindLiveObjects: 0.659400 ms CreateObjectMapping: 0.511200 ms MarkObjects: 3.639200 ms  DeleteObjects: 3.724700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.754 seconds
Refreshing native plugins compatible for Editor in 1.18 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.786 seconds
Domain Reload Profiling: 1541ms
	BeginReloadAssembly (191ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (503ms)
		LoadAssemblies (412ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (181ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (161ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (787ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (614ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (150ms)
			ProcessInitializeOnLoadAttributes (387ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.2 MB). Loaded Objects now: 8400.
Memory consumption went from 188.5 MB to 178.3 MB.
Total: 8.412700 ms (FindLiveObjects: 0.653100 ms CreateObjectMapping: 0.556500 ms MarkObjects: 3.604300 ms  DeleteObjects: 3.598200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.739 seconds
Refreshing native plugins compatible for Editor in 0.90 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.751 seconds
Domain Reload Profiling: 1492ms
	BeginReloadAssembly (173ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (505ms)
		LoadAssemblies (390ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (193ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (171ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (752ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (561ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (330ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.2 MB). Loaded Objects now: 8402.
Memory consumption went from 190.5 MB to 180.3 MB.
Total: 9.160800 ms (FindLiveObjects: 0.697100 ms CreateObjectMapping: 0.610100 ms MarkObjects: 3.743000 ms  DeleteObjects: 4.109600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7549 unused Assets / (10.3 MB). Loaded Objects now: 8402.
Memory consumption went from 190.6 MB to 180.3 MB.
Total: 10.795100 ms (FindLiveObjects: 0.702300 ms CreateObjectMapping: 0.603800 ms MarkObjects: 5.243100 ms  DeleteObjects: 4.244500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.737 seconds
Refreshing native plugins compatible for Editor in 1.47 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.780 seconds
Domain Reload Profiling: 1518ms
	BeginReloadAssembly (181ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (492ms)
		LoadAssemblies (390ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (161ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (780ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (595ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (163ms)
			ProcessInitializeOnLoadAttributes (360ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.2 MB). Loaded Objects now: 8404.
Memory consumption went from 190.4 MB to 180.2 MB.
Total: 9.245000 ms (FindLiveObjects: 0.675500 ms CreateObjectMapping: 0.669400 ms MarkObjects: 3.917700 ms  DeleteObjects: 3.981600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.720 seconds
Refreshing native plugins compatible for Editor in 1.01 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.756 seconds
Domain Reload Profiling: 1478ms
	BeginReloadAssembly (171ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (489ms)
		LoadAssemblies (377ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (186ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (756ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (579ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (146ms)
			ProcessInitializeOnLoadAttributes (361ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.04 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.3 MB). Loaded Objects now: 8406.
Memory consumption went from 190.5 MB to 180.2 MB.
Total: 9.176600 ms (FindLiveObjects: 0.655000 ms CreateObjectMapping: 0.730700 ms MarkObjects: 3.892200 ms  DeleteObjects: 3.898100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7549 unused Assets / (9.9 MB). Loaded Objects now: 8406.
Memory consumption went from 190.6 MB to 180.7 MB.
Total: 8.579600 ms (FindLiveObjects: 0.649100 ms CreateObjectMapping: 0.533300 ms MarkObjects: 3.918100 ms  DeleteObjects: 3.478300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.753 seconds
Refreshing native plugins compatible for Editor in 0.93 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.774 seconds
Domain Reload Profiling: 1529ms
	BeginReloadAssembly (173ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (520ms)
		LoadAssemblies (387ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (211ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (179ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (775ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (583ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (157ms)
			ProcessInitializeOnLoadAttributes (349ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 1.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.2 MB). Loaded Objects now: 8408.
Memory consumption went from 190.4 MB to 180.2 MB.
Total: 8.490200 ms (FindLiveObjects: 0.669100 ms CreateObjectMapping: 0.573900 ms MarkObjects: 3.534400 ms  DeleteObjects: 3.712000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.863 seconds
Refreshing native plugins compatible for Editor in 0.92 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.781 seconds
Domain Reload Profiling: 1644ms
	BeginReloadAssembly (228ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (24ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (70ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (562ms)
		LoadAssemblies (453ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (197ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (176ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (782ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (595ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (364ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.4 MB). Loaded Objects now: 8410.
Memory consumption went from 190.5 MB to 180.1 MB.
Total: 8.861200 ms (FindLiveObjects: 0.694100 ms CreateObjectMapping: 0.563400 ms MarkObjects: 3.713300 ms  DeleteObjects: 3.889100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7549 unused Assets / (10.4 MB). Loaded Objects now: 8410.
Memory consumption went from 190.6 MB to 180.2 MB.
Total: 10.244400 ms (FindLiveObjects: 0.754300 ms CreateObjectMapping: 0.758500 ms MarkObjects: 4.451000 ms  DeleteObjects: 4.280000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.813 seconds
Refreshing native plugins compatible for Editor in 0.89 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.783 seconds
Domain Reload Profiling: 1599ms
	BeginReloadAssembly (196ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (551ms)
		LoadAssemblies (404ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (229ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (203ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (784ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (598ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (164ms)
			ProcessInitializeOnLoadAttributes (351ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.4 MB). Loaded Objects now: 8412.
Memory consumption went from 190.5 MB to 180.0 MB.
Total: 9.625600 ms (FindLiveObjects: 0.665000 ms CreateObjectMapping: 0.651600 ms MarkObjects: 3.959900 ms  DeleteObjects: 4.348100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.09 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7549 unused Assets / (10.4 MB). Loaded Objects now: 8412.
Memory consumption went from 190.6 MB to 180.2 MB.
Total: 9.682800 ms (FindLiveObjects: 0.755700 ms CreateObjectMapping: 0.644200 ms MarkObjects: 3.860800 ms  DeleteObjects: 4.421200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.796 seconds
Refreshing native plugins compatible for Editor in 0.93 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.812 seconds
Domain Reload Profiling: 1610ms
	BeginReloadAssembly (177ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (548ms)
		LoadAssemblies (412ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (217ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (192ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (813ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (629ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (175ms)
			ProcessInitializeOnLoadAttributes (367ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.5 MB). Loaded Objects now: 8414.
Memory consumption went from 190.5 MB to 180.0 MB.
Total: 10.794700 ms (FindLiveObjects: 0.792800 ms CreateObjectMapping: 0.701700 ms MarkObjects: 4.079800 ms  DeleteObjects: 5.219300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.701 seconds
Refreshing native plugins compatible for Editor in 0.93 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.752 seconds
Domain Reload Profiling: 1455ms
	BeginReloadAssembly (167ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (475ms)
		LoadAssemblies (370ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (178ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (159ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (753ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (578ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (149ms)
			ProcessInitializeOnLoadAttributes (353ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.4 MB). Loaded Objects now: 8416.
Memory consumption went from 190.5 MB to 180.1 MB.
Total: 9.678600 ms (FindLiveObjects: 0.661700 ms CreateObjectMapping: 0.599000 ms MarkObjects: 4.061700 ms  DeleteObjects: 4.355100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 7931.072258 seconds.
  path: Assets/Resources/Scripts/Configs/ImportJsonFile.cs
  artifactKey: Guid(ab6799b1f164de845bef2c0548a11f45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Scripts/Configs/ImportJsonFile.cs using Guid(ab6799b1f164de845bef2c0548a11f45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c99b819e244669ab2e985e2f2fed3062') in 0.1832054 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.046 seconds
Refreshing native plugins compatible for Editor in 0.95 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.839 seconds
Domain Reload Profiling: 1887ms
	BeginReloadAssembly (324ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (44ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (108ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (658ms)
		LoadAssemblies (524ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (232ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (198ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (840ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (637ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (176ms)
			ProcessInitializeOnLoadAttributes (379ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 1.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.4 MB). Loaded Objects now: 8418.
Memory consumption went from 190.5 MB to 180.0 MB.
Total: 9.515100 ms (FindLiveObjects: 0.688800 ms CreateObjectMapping: 0.599700 ms MarkObjects: 4.022200 ms  DeleteObjects: 4.203100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.750 seconds
Refreshing native plugins compatible for Editor in 1.42 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.785 seconds
Domain Reload Profiling: 1536ms
	BeginReloadAssembly (181ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (510ms)
		LoadAssemblies (403ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (188ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (785ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (606ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (168ms)
			ProcessInitializeOnLoadAttributes (364ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.1 MB). Loaded Objects now: 8420.
Memory consumption went from 190.5 MB to 180.4 MB.
Total: 8.574300 ms (FindLiveObjects: 0.637800 ms CreateObjectMapping: 0.552100 ms MarkObjects: 3.692000 ms  DeleteObjects: 3.691800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.763 seconds
Refreshing native plugins compatible for Editor in 1.00 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.756 seconds
Domain Reload Profiling: 1521ms
	BeginReloadAssembly (179ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (510ms)
		LoadAssemblies (402ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (187ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (168ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (757ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (575ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (342ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.1 MB). Loaded Objects now: 8422.
Memory consumption went from 190.5 MB to 180.4 MB.
Total: 8.347000 ms (FindLiveObjects: 0.652200 ms CreateObjectMapping: 0.560200 ms MarkObjects: 3.564800 ms  DeleteObjects: 3.568900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.437 seconds
Refreshing native plugins compatible for Editor in 0.93 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.865 seconds
Domain Reload Profiling: 2305ms
	BeginReloadAssembly (345ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (54ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (116ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (1029ms)
		LoadAssemblies (906ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (225ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (195ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (865ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (661ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (173ms)
			ProcessInitializeOnLoadAttributes (398ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.2 MB). Loaded Objects now: 8424.
Memory consumption went from 190.5 MB to 180.3 MB.
Total: 9.163100 ms (FindLiveObjects: 0.713400 ms CreateObjectMapping: 0.560200 ms MarkObjects: 4.200800 ms  DeleteObjects: 3.686900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.010 seconds
Refreshing native plugins compatible for Editor in 1.15 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.857 seconds
Domain Reload Profiling: 2870ms
	BeginReloadAssembly (400ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (57ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (138ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (1540ms)
		LoadAssemblies (1451ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (219ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (190ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (858ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (653ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (175ms)
			ProcessInitializeOnLoadAttributes (392ms)
			ProcessInitializeOnLoadMethodAttributes (61ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 1.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (11.8 MB). Loaded Objects now: 8426.
Memory consumption went from 190.5 MB to 178.7 MB.
Total: 11.462900 ms (FindLiveObjects: 0.657300 ms CreateObjectMapping: 0.657100 ms MarkObjects: 4.093100 ms  DeleteObjects: 6.053900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.766 seconds
Refreshing native plugins compatible for Editor in 0.91 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.760 seconds
Domain Reload Profiling: 1526ms
	BeginReloadAssembly (183ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (523ms)
		LoadAssemblies (395ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (206ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (184ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (760ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (583ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (345ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.4 MB). Loaded Objects now: 8428.
Memory consumption went from 190.5 MB to 180.1 MB.
Total: 10.234600 ms (FindLiveObjects: 0.730500 ms CreateObjectMapping: 0.668400 ms MarkObjects: 4.335000 ms  DeleteObjects: 4.499900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.051 seconds
Refreshing native plugins compatible for Editor in 0.96 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.802 seconds
Domain Reload Profiling: 2854ms
	BeginReloadAssembly (344ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (55ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (117ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (1639ms)
		LoadAssemblies (1514ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (230ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (198ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (803ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (613ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (154ms)
			ProcessInitializeOnLoadAttributes (381ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (11.2 MB). Loaded Objects now: 8430.
Memory consumption went from 190.5 MB to 179.3 MB.
Total: 11.631300 ms (FindLiveObjects: 0.781000 ms CreateObjectMapping: 0.658400 ms MarkObjects: 4.683600 ms  DeleteObjects: 5.506400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.745 seconds
Refreshing native plugins compatible for Editor in 0.88 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.784 seconds
Domain Reload Profiling: 1531ms
	BeginReloadAssembly (178ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (506ms)
		LoadAssemblies (388ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (194ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (172ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (784ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (610ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (151ms)
			ProcessInitializeOnLoadAttributes (370ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.1 MB). Loaded Objects now: 8432.
Memory consumption went from 190.5 MB to 180.4 MB.
Total: 8.329700 ms (FindLiveObjects: 0.642000 ms CreateObjectMapping: 0.533800 ms MarkObjects: 3.608100 ms  DeleteObjects: 3.545300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.499 seconds
Refreshing native plugins compatible for Editor in 1.03 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.771 seconds
Domain Reload Profiling: 2271ms
	BeginReloadAssembly (183ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (1255ms)
		LoadAssemblies (1118ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (219ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (195ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (771ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (580ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (174ms)
			ProcessInitializeOnLoadAttributes (337ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.0 MB). Loaded Objects now: 8434.
Memory consumption went from 190.5 MB to 180.5 MB.
Total: 8.249600 ms (FindLiveObjects: 0.649700 ms CreateObjectMapping: 0.542000 ms MarkObjects: 3.581600 ms  DeleteObjects: 3.475800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.727 seconds
Refreshing native plugins compatible for Editor in 0.94 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.762 seconds
Domain Reload Profiling: 1490ms
	BeginReloadAssembly (175ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (489ms)
		LoadAssemblies (386ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (181ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (161ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (762ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (588ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (153ms)
			ProcessInitializeOnLoadAttributes (361ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.04 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.2 MB). Loaded Objects now: 8436.
Memory consumption went from 190.5 MB to 180.3 MB.
Total: 8.682500 ms (FindLiveObjects: 0.669700 ms CreateObjectMapping: 0.583100 ms MarkObjects: 3.628800 ms  DeleteObjects: 3.799700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7549 unused Assets / (11.0 MB). Loaded Objects now: 8436.
Memory consumption went from 190.7 MB to 179.7 MB.
Total: 25.243100 ms (FindLiveObjects: 1.085200 ms CreateObjectMapping: 1.298100 ms MarkObjects: 16.343300 ms  DeleteObjects: 6.515000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.775 seconds
Refreshing native plugins compatible for Editor in 0.95 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.807 seconds
Domain Reload Profiling: 1584ms
	BeginReloadAssembly (183ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (525ms)
		LoadAssemblies (383ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (221ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (193ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (807ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (626ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (171ms)
			ProcessInitializeOnLoadAttributes (374ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.3 MB). Loaded Objects now: 8438.
Memory consumption went from 190.5 MB to 180.2 MB.
Total: 9.311400 ms (FindLiveObjects: 0.778700 ms CreateObjectMapping: 0.610700 ms MarkObjects: 3.864600 ms  DeleteObjects: 4.056500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.753 seconds
Refreshing native plugins compatible for Editor in 1.01 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.763 seconds
Domain Reload Profiling: 1517ms
	BeginReloadAssembly (174ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (518ms)
		LoadAssemblies (391ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (203ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (181ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (763ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (579ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (150ms)
			ProcessInitializeOnLoadAttributes (355ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.1 MB). Loaded Objects now: 8440.
Memory consumption went from 190.5 MB to 180.4 MB.
Total: 9.042400 ms (FindLiveObjects: 0.671500 ms CreateObjectMapping: 0.619000 ms MarkObjects: 3.704700 ms  DeleteObjects: 4.046300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.434 seconds
Refreshing native plugins compatible for Editor in 1.17 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.775 seconds
Domain Reload Profiling: 2211ms
	BeginReloadAssembly (317ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (60ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (106ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (1055ms)
		LoadAssemblies (928ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (216ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (188ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (775ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (576ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (156ms)
			ProcessInitializeOnLoadAttributes (347ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.1 MB). Loaded Objects now: 8442.
Memory consumption went from 190.5 MB to 180.4 MB.
Total: 9.353900 ms (FindLiveObjects: 0.669300 ms CreateObjectMapping: 0.528500 ms MarkObjects: 4.117700 ms  DeleteObjects: 4.036700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.794 seconds
Refreshing native plugins compatible for Editor in 1.05 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.767 seconds
Domain Reload Profiling: 1563ms
	BeginReloadAssembly (191ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (528ms)
		LoadAssemblies (411ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (201ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (180ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (768ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (590ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (146ms)
			ProcessInitializeOnLoadAttributes (362ms)
			ProcessInitializeOnLoadMethodAttributes (61ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 1.52 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.7 MB). Loaded Objects now: 8444.
Memory consumption went from 190.5 MB to 179.9 MB.
Total: 11.483500 ms (FindLiveObjects: 0.944100 ms CreateObjectMapping: 0.877300 ms MarkObjects: 4.489000 ms  DeleteObjects: 5.172300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.768 seconds
Refreshing native plugins compatible for Editor in 0.93 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.815 seconds
Domain Reload Profiling: 1585ms
	BeginReloadAssembly (195ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (509ms)
		LoadAssemblies (403ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (192ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (168ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (816ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (594ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (154ms)
			ProcessInitializeOnLoadAttributes (361ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 0.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.1 MB). Loaded Objects now: 8446.
Memory consumption went from 190.5 MB to 180.4 MB.
Total: 8.682900 ms (FindLiveObjects: 0.646600 ms CreateObjectMapping: 0.774000 ms MarkObjects: 3.713900 ms  DeleteObjects: 3.547700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7548 unused Assets / (10.2 MB). Loaded Objects now: 8445.
Memory consumption went from 190.6 MB to 180.4 MB.
Total: 9.058600 ms (FindLiveObjects: 0.671600 ms CreateObjectMapping: 0.564000 ms MarkObjects: 3.861600 ms  DeleteObjects: 3.959900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.747 seconds
Refreshing native plugins compatible for Editor in 2.91 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.792 seconds
Domain Reload Profiling: 1541ms
	BeginReloadAssembly (172ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (515ms)
		LoadAssemblies (382ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (209ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (181ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (792ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (589ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (154ms)
			ProcessInitializeOnLoadAttributes (350ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7561 unused Assets / (10.2 MB). Loaded Objects now: 8447.
Memory consumption went from 190.5 MB to 180.3 MB.
Total: 9.130300 ms (FindLiveObjects: 0.653200 ms CreateObjectMapping: 0.558800 ms MarkObjects: 3.978300 ms  DeleteObjects: 3.939200 ms)

Prepare: number of updated asset objects reloaded= 0
